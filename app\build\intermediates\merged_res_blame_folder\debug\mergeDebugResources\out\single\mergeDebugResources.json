[{"merged": "com.example.todosqlite.app-debug-45:/menu_bottom_navigation_menu.xml.flat", "source": "com.example.todosqlite.app-main-47:/menu/bottom_navigation_menu.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_check.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_check.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_more_vert.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_more_vert.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_task.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_task.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_add.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_add.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.todosqlite.app-debug-45:/layout_fragment_task.xml.flat", "source": "com.example.todosqlite.app-main-47:/layout/fragment_task.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_empty_tasks.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_empty_tasks.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_search.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_search.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.todosqlite.app-debug-45:/layout_nav_header_main.xml.flat", "source": "com.example.todosqlite.app-main-47:/layout/nav_header_main.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_menu.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_menu.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_priority_background.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/priority_background.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/layout_item_task.xml.flat", "source": "com.example.todosqlite.app-main-47:/layout/item_task.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_nav_header_bg.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/nav_header_bg.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_person.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_person.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/xml_backup_rules.xml.flat", "source": "com.example.todosqlite.app-main-47:/xml/backup_rules.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_launcher_background.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_settings.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_settings.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_pending.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_pending.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/menu_drawer_menu.xml.flat", "source": "com.example.todosqlite.app-main-47:/menu/drawer_menu.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/layout_activity_main.xml.flat", "source": "com.example.todosqlite.app-main-47:/layout/activity_main.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_calendar.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_calendar.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_work.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_work.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/layout_item_category.xml.flat", "source": "com.example.todosqlite.app-main-47:/layout/item_category.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/xml_data_extraction_rules.xml.flat", "source": "com.example.todosqlite.app-main-47:/xml/data_extraction_rules.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_circle_background.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/circle_background.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_time.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_time.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/drawable_ic_info.xml.flat", "source": "com.example.todosqlite.app-main-47:/drawable/ic_info.xml"}, {"merged": "com.example.todosqlite.app-debug-45:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.todosqlite.app-main-47:/mipmap-mdpi/ic_launcher.webp"}]