<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    
    <!-- Navigation header dimensions -->
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    
    <!-- Fab margin -->
    <dimen name="fab_margin">16dp</dimen>
    
    <!-- Card dimensions -->
    <dimen name="card_margin">8dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">3dp</dimen>
    
    <!-- Text sizes -->
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <dimen name="text_size_tiny">12sp</dimen>
</resources>
