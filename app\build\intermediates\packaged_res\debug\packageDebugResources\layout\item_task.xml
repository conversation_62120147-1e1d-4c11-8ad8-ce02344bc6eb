<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/card_stroke_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Checkbox -->
        <CheckBox
            android:id="@+id/cb_task_completed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginEnd="12dp" />

        <!-- Task Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Task Title -->
            <TextView
                android:id="@+id/tv_task_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tiêu đề nhiệm vụ"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                android:maxLines="2"
                android:ellipsize="end" />

            <!-- Task Description -->
            <TextView
                android:id="@+id/tv_task_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Mô tả nhiệm vụ"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginTop="4dp"
                android:maxLines="3"
                android:ellipsize="end"
                android:visibility="gone"
                tools:visibility="visible" />

            <!-- Task Info Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical">

                <!-- Category Indicator -->
                <View
                    android:id="@+id/view_category_indicator"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:background="@drawable/circle_background"
                    android:layout_marginEnd="8dp" />

                <!-- Time Info -->
                <TextView
                    android:id="@+id/tv_task_time"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Hôm nay, 10:00"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:drawableStart="@drawable/ic_time"
                    android:drawablePadding="4dp"
                    android:gravity="center_vertical" />

                <!-- Priority Indicator -->
                <TextView
                    android:id="@+id/tv_priority"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cao"
                    android:textSize="10sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:background="@drawable/priority_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- More Options -->
        <ImageButton
            android:id="@+id/btn_task_options"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="top"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_more_vert"
            android:contentDescription="Tùy chọn"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
