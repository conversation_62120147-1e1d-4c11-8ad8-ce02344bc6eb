1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.todosqlite"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.todosqlite.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.todosqlite.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:5:5-34:19
18        android:allowBackup="true"
18-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.TodoSqlite" >
29-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:13:9-48
30        <activity
30-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:15:9-23:20
31            android:name="com.example.todosqlite.MainActivity"
31-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:16:13-41
32            android:exported="true" >
32-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:17:13-36
33            <intent-filter>
33-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:18:13-22:29
34                <action android:name="android.intent.action.MAIN" />
34-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:19:17-69
34-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:19:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:21:17-77
36-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:21:27-74
37            </intent-filter>
38        </activity>
39        <activity
39-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:25:9-28:58
40            android:name="com.example.todosqlite.activities.AddTaskActivity"
40-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:26:13-55
41            android:exported="false"
41-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:27:13-37
42            android:parentActivityName="com.example.todosqlite.MainActivity" />
42-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:28:13-55
43        <activity
43-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:30:9-33:58
44            android:name="com.example.todosqlite.activities.AddCategoryActivity"
44-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:31:13-59
45            android:exported="false"
45-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:32:13-37
46            android:parentActivityName="com.example.todosqlite.MainActivity" />
46-->D:\TodoSqlite\app\src\main\AndroidManifest.xml:33:13-55
47
48        <service
48-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a251b7feeaca4f8967d31bbde036aa3a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
49            android:name="androidx.room.MultiInstanceInvalidationService"
49-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a251b7feeaca4f8967d31bbde036aa3a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
50            android:directBootAware="true"
50-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a251b7feeaca4f8967d31bbde036aa3a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
51            android:exported="false" />
51-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a251b7feeaca4f8967d31bbde036aa3a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
52
53        <provider
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.example.todosqlite.androidx-startup"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\b3302bc95f3742ec5a56532917b0a0d6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\b3302bc95f3742ec5a56532917b0a0d6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\b3302bc95f3742ec5a56532917b0a0d6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <uses-library
68-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\33d7af5859d4b0ca542a42be2f1d09a8\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
69            android:name="androidx.window.extensions"
69-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\33d7af5859d4b0ca542a42be2f1d09a8\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
70            android:required="false" />
70-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\33d7af5859d4b0ca542a42be2f1d09a8\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
71        <uses-library
71-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\33d7af5859d4b0ca542a42be2f1d09a8\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
72            android:name="androidx.window.sidecar"
72-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\33d7af5859d4b0ca542a42be2f1d09a8\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
73            android:required="false" />
73-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\33d7af5859d4b0ca542a42be2f1d09a8\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
74
75        <receiver
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
76            android:name="androidx.profileinstaller.ProfileInstallReceiver"
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
77            android:directBootAware="false"
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
78            android:enabled="true"
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
79            android:exported="true"
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
80            android:permission="android.permission.DUMP" >
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
82                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
85                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
88                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
91                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
92            </intent-filter>
93        </receiver>
94    </application>
95
96</manifest>
