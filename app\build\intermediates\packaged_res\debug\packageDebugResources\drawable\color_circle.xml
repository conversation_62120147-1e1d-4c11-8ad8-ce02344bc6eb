<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="3dp"
                android:color="?attr/colorPrimary" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="1dp"
                android:color="@color/card_stroke_color" />
        </shape>
    </item>
</selector>
