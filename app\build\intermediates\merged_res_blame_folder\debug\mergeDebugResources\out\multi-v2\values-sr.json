{"logs": [{"outputFile": "com.example.todosqlite.app-mergeDebugResources-47:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\55b720bce712d5f10fb40e251130c91a\\transformed\\core-1.13.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3531,3629,3731,3828,3932,4036,4141,10175", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3624,3726,3823,3927,4031,4136,4252,10271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\76a693482785115f724fb0ceaef9fe4b\\transformed\\appcompat-1.7.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,478,579,685,771,875,997,1081,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2616,2725,2832,3002,9852", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "473,574,680,766,870,992,1076,1157,1248,1341,1436,1530,1630,1723,1818,1923,2014,2105,2191,2296,2402,2505,2611,2720,2827,2997,3094,9934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\db0f45af4afa27c4564c6b21d1e85aea\\transformed\\navigation-ui-2.7.6\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,122", "endOffsets": "157,280"}, "to": {"startLines": "112,113", "startColumns": "4,4", "startOffsets": "9542,9649", "endColumns": "106,122", "endOffsets": "9644,9767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9a29a62ff3fc1ea4fe1f5798df91a5f9\\transformed\\material-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,660,753,847,978,1059,1121,1187,1279,1347,1410,1513,1573,1639,1695,1766,1826,1880,1992,2049,2110,2164,2240,2365,2451,2528,2621,2705,2788,2926,3007,3090,3221,3309,3387,3441,3497,3563,3637,3715,3786,3868,3943,4019,4094,4165,4272,4362,4435,4527,4623,4695,4771,4867,4920,5002,5069,5156,5243,5305,5369,5432,5501,5606,5716,5812,5920,5978,6038,6118,6201,6277", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,75,75,79,106,92,93,130,80,61,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "316,392,468,548,655,748,842,973,1054,1116,1182,1274,1342,1405,1508,1568,1634,1690,1761,1821,1875,1987,2044,2105,2159,2235,2360,2446,2523,2616,2700,2783,2921,3002,3085,3216,3304,3382,3436,3492,3558,3632,3710,3781,3863,3938,4014,4089,4160,4267,4357,4430,4522,4618,4690,4766,4862,4915,4997,5064,5151,5238,5300,5364,5427,5496,5601,5711,5807,5915,5973,6033,6113,6196,6272,6349"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3099,3175,3251,3331,3438,4257,4351,4482,4563,4625,4691,4783,4851,4914,5017,5077,5143,5199,5270,5330,5384,5496,5553,5614,5668,5744,5869,5955,6032,6125,6209,6292,6430,6511,6594,6725,6813,6891,6945,7001,7067,7141,7219,7290,7372,7447,7523,7598,7669,7776,7866,7939,8031,8127,8199,8275,8371,8424,8506,8573,8660,8747,8809,8873,8936,9005,9110,9220,9316,9424,9482,9772,9939,10022,10098", "endLines": "6,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114,116,117,118", "endColumns": "12,75,75,79,106,92,93,130,80,61,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "366,3170,3246,3326,3433,3526,4346,4477,4558,4620,4686,4778,4846,4909,5012,5072,5138,5194,5265,5325,5379,5491,5548,5609,5663,5739,5864,5950,6027,6120,6204,6287,6425,6506,6589,6720,6808,6886,6940,6996,7062,7136,7214,7285,7367,7442,7518,7593,7664,7771,7861,7934,8026,8122,8194,8270,8366,8419,8501,8568,8655,8742,8804,8868,8931,9000,9105,9215,9311,9419,9477,9537,9847,10017,10093,10170"}}]}]}