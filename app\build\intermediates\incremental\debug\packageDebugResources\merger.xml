<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TodoSqlite\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TodoSqlite\app\src\main\res"><file name="ic_launcher_background" path="D:\TodoSqlite\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\TodoSqlite\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\TodoSqlite\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\TodoSqlite\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TodoSqlite\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TodoSqlite\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TodoSqlite\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TodoSqlite\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TodoSqlite\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TodoSqlite\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TodoSqlite\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TodoSqlite\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TodoSqlite\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TodoSqlite\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TodoSqlite\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\TodoSqlite\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="background_color">#FAFAFA</color><color name="primary_dark_color">#1976D2</color><color name="card_stroke_color">#E0E0E0</color><color name="category_work">#FF9800</color><color name="priority_low">#4CAF50</color><color name="priority_high">#F44336</color><color name="category_personal">#4CAF50</color><color name="category_all">#2196F3</color><color name="accent_color">#FF4081</color><color name="surface_color">#FFFFFF</color><color name="priority_medium">#FF9800</color><color name="divider_color">#E0E0E0</color><color name="primary_color">#2196F3</color></file><file path="D:\TodoSqlite\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">TodoSqlite</string></file><file path="D:\TodoSqlite\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.TodoSqlite" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.TodoSqlite" parent="Base.Theme.TodoSqlite"/></file><file path="D:\TodoSqlite\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.TodoSqlite" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="D:\TodoSqlite\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\TodoSqlite\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="circle_background" path="D:\TodoSqlite\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\TodoSqlite\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="D:\TodoSqlite\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_check" path="D:\TodoSqlite\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_empty_tasks" path="D:\TodoSqlite\app\src\main\res\drawable\ic_empty_tasks.xml" qualifiers="" type="drawable"/><file name="ic_info" path="D:\TodoSqlite\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="D:\TodoSqlite\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="D:\TodoSqlite\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_pending" path="D:\TodoSqlite\app\src\main\res\drawable\ic_pending.xml" qualifiers="" type="drawable"/><file name="ic_person" path="D:\TodoSqlite\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_search" path="D:\TodoSqlite\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\TodoSqlite\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_task" path="D:\TodoSqlite\app\src\main\res\drawable\ic_task.xml" qualifiers="" type="drawable"/><file name="ic_time" path="D:\TodoSqlite\app\src\main\res\drawable\ic_time.xml" qualifiers="" type="drawable"/><file name="ic_work" path="D:\TodoSqlite\app\src\main\res\drawable\ic_work.xml" qualifiers="" type="drawable"/><file name="nav_header_bg" path="D:\TodoSqlite\app\src\main\res\drawable\nav_header_bg.xml" qualifiers="" type="drawable"/><file name="priority_background" path="D:\TodoSqlite\app\src\main\res\drawable\priority_background.xml" qualifiers="" type="drawable"/><file name="fragment_task" path="D:\TodoSqlite\app\src\main\res\layout\fragment_task.xml" qualifiers="" type="layout"/><file name="item_category" path="D:\TodoSqlite\app\src\main\res\layout\item_category.xml" qualifiers="" type="layout"/><file name="item_task" path="D:\TodoSqlite\app\src\main\res\layout\item_task.xml" qualifiers="" type="layout"/><file name="nav_header_main" path="D:\TodoSqlite\app\src\main\res\layout\nav_header_main.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="D:\TodoSqlite\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="drawer_menu" path="D:\TodoSqlite\app\src\main\res\menu\drawer_menu.xml" qualifiers="" type="menu"/><file path="D:\TodoSqlite\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="nav_header_vertical_spacing">8dp</dimen><dimen name="nav_header_height">176dp</dimen><dimen name="fab_margin">16dp</dimen><dimen name="card_margin">8dp</dimen><dimen name="card_corner_radius">12dp</dimen><dimen name="card_elevation">3dp</dimen><dimen name="text_size_large">18sp</dimen><dimen name="text_size_medium">16sp</dimen><dimen name="text_size_small">14sp</dimen><dimen name="text_size_tiny">12sp</dimen></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TodoSqlite\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TodoSqlite\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TodoSqlite\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TodoSqlite\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>