#Tue Jul 01 15:38:10 ICT 2025
com.example.todosqlite.app-main-5\:/drawable/ic_person.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_person.xml
com.example.todosqlite.app-main-5\:/layout/item_task.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_task.xml
com.example.todosqlite.app-main-5\:/drawable/ic_launcher_foreground.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.example.todosqlite.app-main-5\:/drawable/ic_time.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_time.xml
com.example.todosqlite.app-main-5\:/xml/data_extraction_rules.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.example.todosqlite.app-main-5\:/drawable/circle_background.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_background.xml
com.example.todosqlite.app-main-5\:/drawable/ic_info.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_info.xml
com.example.todosqlite.app-main-5\:/layout/nav_header_main.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\nav_header_main.xml
com.example.todosqlite.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.todosqlite.app-main-5\:/drawable/ic_menu.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_menu.xml
com.example.todosqlite.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.todosqlite.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.todosqlite.app-main-5\:/layout/item_category.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_category.xml
com.example.todosqlite.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.todosqlite.app-main-5\:/drawable/ic_launcher_background.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.example.todosqlite.app-main-5\:/drawable/ic_check.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check.xml
com.example.todosqlite.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.todosqlite.app-main-5\:/drawable/ic_calendar.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_calendar.xml
com.example.todosqlite.app-main-5\:/drawable/ic_search.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_search.xml
com.example.todosqlite.app-main-5\:/menu/drawer_menu.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\drawer_menu.xml
com.example.todosqlite.app-main-5\:/layout/fragment_task.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_task.xml
com.example.todosqlite.app-main-5\:/drawable/ic_settings.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.xml
com.example.todosqlite.app-main-5\:/drawable/ic_add.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.xml
com.example.todosqlite.app-main-5\:/drawable/ic_more_vert.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_more_vert.xml
com.example.todosqlite.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.todosqlite.app-main-5\:/mipmap-anydpi/ic_launcher.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher.xml
com.example.todosqlite.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.todosqlite.app-main-5\:/drawable/nav_header_bg.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\nav_header_bg.xml
com.example.todosqlite.app-main-5\:/drawable/ic_work.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_work.xml
com.example.todosqlite.app-main-5\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher_round.xml
com.example.todosqlite.app-main-5\:/menu/bottom_navigation_menu.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_navigation_menu.xml
com.example.todosqlite.app-main-5\:/mipmap-hdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.todosqlite.app-main-5\:/xml/backup_rules.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.example.todosqlite.app-main-5\:/drawable/priority_background.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\priority_background.xml
com.example.todosqlite.app-main-5\:/layout/activity_main.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.example.todosqlite.app-main-5\:/mipmap-mdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.todosqlite.app-main-5\:/drawable/ic_empty_tasks.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_empty_tasks.xml
com.example.todosqlite.app-main-5\:/drawable/ic_pending.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_pending.xml
com.example.todosqlite.app-main-5\:/drawable/ic_task.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_task.xml
com.example.todosqlite.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
