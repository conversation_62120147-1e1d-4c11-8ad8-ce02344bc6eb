R_DEF: Internal format may change without notice
local
color accent_color
color background_color
color black
color card_stroke_color
color category_all
color category_personal
color category_work
color divider_color
color primary_color
color primary_dark_color
color priority_high
color priority_low
color priority_medium
color surface_color
color white
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen card_corner_radius
dimen card_elevation
dimen card_margin
dimen fab_margin
dimen nav_header_height
dimen nav_header_vertical_spacing
dimen text_size_large
dimen text_size_medium
dimen text_size_small
dimen text_size_tiny
drawable category_color_circle
drawable circle_background
drawable color_circle
drawable ic_add
drawable ic_arrow_forward
drawable ic_calendar
drawable ic_calendar_empty
drawable ic_check
drawable ic_empty_tasks
drawable ic_info
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_menu
drawable ic_more_vert
drawable ic_notifications
drawable ic_pending
drawable ic_person
drawable ic_search
drawable ic_settings
drawable ic_task
drawable ic_time
drawable ic_work
drawable nav_header_bg
drawable priority_background
drawable rounded_background
drawable spinner_background
drawable time_selector_background
id app_bar_layout
id bottom_navigation
id btn_cancel
id btn_save
id btn_task_options
id calendar_view
id cb_task_completed
id color_1
id color_2
id color_3
id color_4
id color_5
id color_6
id color_7
id color_8
id drawer_layout
id et_category_description
id et_category_name
id et_task_description
id et_task_title
id fab_add_category
id fab_add_task
id fragment_container
id imageView
id layout_empty_day
id layout_empty_state
id nav_about
id nav_all_tasks
id nav_calendar
id nav_completed_tasks
id nav_pending_tasks
id nav_personal
id nav_profile
id nav_settings
id nav_tasks
id nav_view
id nav_work
id rv_categories
id rv_day_tasks
id rv_tasks
id search_view
id spinner_category
id spinner_priority
id toolbar
id tv_category_name
id tv_completed_tasks
id tv_end_time
id tv_pending_tasks
id tv_priority
id tv_selected_date
id tv_start_time
id tv_task_count
id tv_task_count_for_day
id tv_task_description
id tv_task_time
id tv_task_title
id tv_total_tasks
id tv_user_email
id tv_user_name
id view_category_color
id view_category_indicator
id view_selected_color
layout activity_add_category
layout activity_add_task
layout activity_main
layout fragment_calendar
layout fragment_profile
layout fragment_task
layout item_category
layout item_task
layout nav_header_main
menu bottom_navigation_menu
menu drawer_menu
mipmap ic_launcher
mipmap ic_launcher_round
string add
string app_name
string cancel
string category_added
string category_all
string category_color
string category_deleted
string category_description
string category_name
string category_updated
string delete
string edit
string empty_categories
string empty_tasks
string empty_tasks_subtitle
string nav_about
string nav_all_tasks
string nav_calendar
string nav_completed_tasks
string nav_pending_tasks
string nav_personal
string nav_profile
string nav_settings
string nav_tasks
string nav_work
string navigation_drawer_close
string navigation_drawer_open
string ok
string priority_high
string priority_low
string priority_medium
string save
string search
string search_hint
string task_added
string task_category
string task_completed
string task_deleted
string task_description
string task_end_time
string task_pending
string task_priority
string task_start_time
string task_title
string task_updated
style Base.Theme.TodoSqlite
style Theme.TodoSqlite
xml backup_rules
xml data_extraction_rules
