R_DEF: Internal format may change without notice
local
color accent_color
color background_color
color black
color card_stroke_color
color category_all
color category_personal
color category_work
color divider_color
color primary_color
color primary_dark_color
color priority_high
color priority_low
color priority_medium
color surface_color
color white
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen card_corner_radius
dimen card_elevation
dimen card_margin
dimen fab_margin
dimen nav_header_height
dimen nav_header_vertical_spacing
dimen text_size_large
dimen text_size_medium
dimen text_size_small
dimen text_size_tiny
drawable circle_background
drawable ic_add
drawable ic_calendar
drawable ic_check
drawable ic_empty_tasks
drawable ic_info
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_menu
drawable ic_more_vert
drawable ic_pending
drawable ic_person
drawable ic_search
drawable ic_settings
drawable ic_task
drawable ic_time
drawable ic_work
drawable nav_header_bg
drawable priority_background
id app_bar_layout
id bottom_navigation
id btn_menu
id btn_task_options
id cb_task_completed
id drawer_layout
id fab_add_category
id fab_add_task
id imageView
id layout_empty_state
id nav_about
id nav_all_tasks
id nav_calendar
id nav_completed_tasks
id nav_pending_tasks
id nav_personal
id nav_profile
id nav_settings
id nav_tasks
id nav_view
id nav_work
id rv_categories
id rv_tasks
id search_view
id toolbar
id tv_category_name
id tv_priority
id tv_task_count
id tv_task_description
id tv_task_time
id tv_task_title
id tv_user_email
id tv_user_name
id view_category_color
id view_category_indicator
layout activity_main
layout fragment_task
layout item_category
layout item_task
layout nav_header_main
menu bottom_navigation_menu
menu drawer_menu
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
style Base.Theme.TodoSqlite
style Theme.TodoSqlite
xml backup_rules
xml data_extraction_rules
