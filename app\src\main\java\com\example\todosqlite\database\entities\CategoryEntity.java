package com.example.todosqlite.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import java.util.Date;

/**
 * Room Entity cho Category
 */
@Entity(
    tableName = "categories",
    foreignKeys = @ForeignKey(
        entity = UserEntity.class,
        parentColumns = "id",
        childColumns = "user_id",
        onDelete = ForeignKey.CASCADE
    ),
    indices = {@Index("user_id")}
)
public class CategoryEntity {
    
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    public int id;
    
    @ColumnInfo(name = "name")
    public String name;
    
    @ColumnInfo(name = "description")
    public String description;
    
    @ColumnInfo(name = "color")
    public String color;
    
    @ColumnInfo(name = "icon")
    public String icon;
    
    @ColumnInfo(name = "user_id")
    public int userId;
    
    @ColumnInfo(name = "task_count")
    public int taskCount;
    
    @ColumnInfo(name = "created_at")
    public Long createdAt; // Timestamp
    
    @ColumnInfo(name = "updated_at")
    public Long updatedAt; // Timestamp
    
    // Constructor mặc định (required by Room)
    public CategoryEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.taskCount = 0;
        this.color = "#2196F3"; // Màu xanh mặc định
        this.icon = "folder";   // Icon mặc định
    }
    
    // Constructor với tên
    public CategoryEntity(String name, int userId) {
        this();
        this.name = name;
        this.userId = userId;
    }
    
    // Constructor đầy đủ
    public CategoryEntity(String name, String description, String color, String icon, int userId) {
        this();
        this.name = name;
        this.description = description;
        this.color = color;
        this.icon = icon;
        this.userId = userId;
    }
    
    // Getters và Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getTaskCount() {
        return taskCount;
    }

    public void setTaskCount(int taskCount) {
        this.taskCount = taskCount;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Helper methods để convert Date
    public Date getCreatedAtAsDate() {
        return createdAt != null ? new Date(createdAt) : null;
    }
    
    public Date getUpdatedAtAsDate() {
        return updatedAt != null ? new Date(updatedAt) : null;
    }
}
