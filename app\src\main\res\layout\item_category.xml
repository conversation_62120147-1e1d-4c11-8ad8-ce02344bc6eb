<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="20dp"
    app:cardElevation="3dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/card_stroke_color">

    <LinearLayout
        android:layout_width="100dp"
        android:layout_height="60dp"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <!-- Color indicator circle -->
        <View
            android:id="@+id/view_category_color"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@drawable/category_color_circle"
            android:layout_marginEnd="8dp" />

        <!-- Text content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="start">

            <TextView
                android:id="@+id/tv_category_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tất cả"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/tv_task_count"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0 task"
                android:textSize="9sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginTop="1dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
