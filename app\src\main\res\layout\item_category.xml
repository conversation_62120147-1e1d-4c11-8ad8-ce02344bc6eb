<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/card_stroke_color">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp"
        android:gravity="center"
        android:minWidth="80dp">

        <View
            android:id="@+id/view_category_color"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/circle_background"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_category_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Tất cả"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/tv_task_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0"
            android:textSize="10sp"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:layout_marginTop="2dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
