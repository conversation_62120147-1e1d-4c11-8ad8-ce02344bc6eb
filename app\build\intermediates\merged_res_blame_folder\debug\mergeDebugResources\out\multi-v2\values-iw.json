{"logs": [{"outputFile": "com.example.todosqlite.app-mergeDebugResources-47:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\55b720bce712d5f10fb40e251130c91a\\transformed\\core-1.13.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "39,40,41,42,43,44,45,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3437,3531,3633,3730,3827,3928,4028,9807", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3526,3628,3725,3822,3923,4023,4129,9903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\76a693482785115f724fb0ceaef9fe4b\\transformed\\appcompat-1.7.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "361,466,566,674,758,860,976,1055,1133,1224,1318,1412,1506,1606,1699,1794,1887,1978,2070,2151,2256,2359,2457,2562,2664,2766,2920,9492", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "461,561,669,753,855,971,1050,1128,1219,1313,1407,1501,1601,1694,1789,1882,1973,2065,2146,2251,2354,2452,2557,2659,2761,2915,3012,9569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\db0f45af4afa27c4564c6b21d1e85aea\\transformed\\navigation-ui-2.7.6\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,119", "endOffsets": "156,276"}, "to": {"startLines": "112,113", "startColumns": "4,4", "startOffsets": "9191,9297", "endColumns": "105,119", "endOffsets": "9292,9412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9a29a62ff3fc1ea4fe1f5798df91a5f9\\transformed\\material-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1077,1142,1230,1300,1363,1455,1518,1578,1637,1700,1761,1815,1917,1974,2033,2087,2155,2266,2347,2422,2509,2589,2671,2803,2874,2947,3071,3159,3235,3288,3342,3408,3481,3557,3628,3706,3776,3851,3933,4001,4102,4187,4257,4347,4438,4512,4585,4674,4725,4806,4873,4955,5040,5102,5166,5229,5297,5391,5486,5576,5673,5730,5788,5863,5945,6020", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "306,383,458,535,635,726,819,932,1012,1072,1137,1225,1295,1358,1450,1513,1573,1632,1695,1756,1810,1912,1969,2028,2082,2150,2261,2342,2417,2504,2584,2666,2798,2869,2942,3066,3154,3230,3283,3337,3403,3476,3552,3623,3701,3771,3846,3928,3996,4097,4182,4252,4342,4433,4507,4580,4669,4720,4801,4868,4950,5035,5097,5161,5224,5292,5386,5481,5571,5668,5725,5783,5858,5940,6015,6091"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3017,3094,3169,3246,3346,4134,4227,4340,4420,4480,4545,4633,4703,4766,4858,4921,4981,5040,5103,5164,5218,5320,5377,5436,5490,5558,5669,5750,5825,5912,5992,6074,6206,6277,6350,6474,6562,6638,6691,6745,6811,6884,6960,7031,7109,7179,7254,7336,7404,7505,7590,7660,7750,7841,7915,7988,8077,8128,8209,8276,8358,8443,8505,8569,8632,8700,8794,8889,8979,9076,9133,9417,9574,9656,9731", "endLines": "6,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,114,116,117,118", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "356,3089,3164,3241,3341,3432,4222,4335,4415,4475,4540,4628,4698,4761,4853,4916,4976,5035,5098,5159,5213,5315,5372,5431,5485,5553,5664,5745,5820,5907,5987,6069,6201,6272,6345,6469,6557,6633,6686,6740,6806,6879,6955,7026,7104,7174,7249,7331,7399,7500,7585,7655,7745,7836,7910,7983,8072,8123,8204,8271,8353,8438,8500,8564,8627,8695,8789,8884,8974,9071,9128,9186,9487,9651,9726,9802"}}]}]}