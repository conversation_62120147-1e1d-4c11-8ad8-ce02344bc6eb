package com.example.todosqlite.repository;

import android.app.Application;

import androidx.lifecycle.LiveData;

import com.example.todosqlite.database.TodoDatabase;
import com.example.todosqlite.database.dao.CategoryDao;
import com.example.todosqlite.database.dao.TaskDao;
import com.example.todosqlite.database.dao.UserDao;
import com.example.todosqlite.database.entities.CategoryEntity;
import com.example.todosqlite.database.entities.TaskEntity;
import com.example.todosqlite.database.entities.UserEntity;

import java.util.List;
import java.util.concurrent.Future;

/**
 * Repository class để quản lý data sources
 * Cung cấp clean API cho UI để access data
 */
public class TodoRepository {
    
    private UserDao userDao;
    private CategoryDao categoryDao;
    private TaskDao taskDao;
    
    // LiveData cho UI observe
    private LiveData<List<UserEntity>> allUsers;
    private LiveData<List<CategoryEntity>> allCategories;
    private LiveData<List<TaskEntity>> allTasks;
    
    public TodoRepository(Application application) {
        TodoDatabase db = TodoDatabase.getDatabase(application);
        userDao = db.userDao();
        categoryDao = db.categoryDao();
        taskDao = db.taskDao();
        
        // Initialize LiveData
        allUsers = userDao.getAllUsersLive();
    }
    
    // ==================== USER OPERATIONS ====================
    
    public LiveData<List<UserEntity>> getAllUsers() {
        return allUsers;
    }
    
    public LiveData<UserEntity> getUserById(int userId) {
        return userDao.getUserByIdLive(userId);
    }
    
    public Future<Long> insertUser(UserEntity user) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> userDao.insertUser(user));
    }
    
    public void updateUser(UserEntity user) {
        TodoDatabase.databaseWriteExecutor.execute(() -> userDao.updateUser(user));
    }
    
    public void deleteUser(UserEntity user) {
        TodoDatabase.databaseWriteExecutor.execute(() -> userDao.deleteUser(user));
    }
    
    public Future<UserEntity> getUserByUsername(String username) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> userDao.getUserByUsername(username));
    }
    
    public void updateLastLogin(int userId) {
        TodoDatabase.databaseWriteExecutor.execute(() -> 
            userDao.updateLastLogin(userId, System.currentTimeMillis()));
    }
    
    // ==================== CATEGORY OPERATIONS ====================
    
    public LiveData<List<CategoryEntity>> getAllCategoriesByUser(int userId) {
        return categoryDao.getAllCategoriesByUserLive(userId);
    }
    
    public LiveData<List<CategoryEntity>> getCategoriesWithTaskCount(int userId) {
        return categoryDao.getCategoriesWithTaskCountLive(userId);
    }
    
    public LiveData<CategoryEntity> getCategoryById(int categoryId) {
        return categoryDao.getCategoryByIdLive(categoryId);
    }
    
    public Future<Long> insertCategory(CategoryEntity category) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> categoryDao.insertCategory(category));
    }
    
    public void updateCategory(CategoryEntity category) {
        TodoDatabase.databaseWriteExecutor.execute(() -> categoryDao.updateCategory(category));
    }
    
    public void deleteCategory(CategoryEntity category) {
        TodoDatabase.databaseWriteExecutor.execute(() -> categoryDao.deleteCategory(category));
    }
    
    public void updateCategoryTaskCount(int categoryId) {
        TodoDatabase.databaseWriteExecutor.execute(() -> 
            categoryDao.updateTaskCount(categoryId, System.currentTimeMillis()));
    }
    
    public Future<CategoryEntity> getCategoryByName(String name, int userId) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> 
            categoryDao.getCategoryByName(name, userId));
    }
    
    // ==================== TASK OPERATIONS ====================
    
    public LiveData<List<TaskEntity>> getAllTasksByUser(int userId) {
        return taskDao.getAllTasksByUserLive(userId);
    }
    
    public LiveData<List<TaskEntity>> getTasksByCategory(int categoryId) {
        return taskDao.getTasksByCategoryLive(categoryId);
    }
    
    public LiveData<List<TaskEntity>> getCompletedTasks(int userId) {
        return taskDao.getCompletedTasksLive(userId);
    }
    
    public LiveData<List<TaskEntity>> getPendingTasks(int userId) {
        return taskDao.getPendingTasksLive(userId);
    }
    
    public LiveData<TaskEntity> getTaskById(int taskId) {
        return taskDao.getTaskByIdLive(taskId);
    }
    
    public LiveData<List<TaskEntity>> searchTasks(int userId, String query) {
        return taskDao.searchTasksLive(userId, query);
    }
    
    public LiveData<List<TaskEntity>> getTasksForToday(int userId, long todayTimestamp) {
        return taskDao.getTasksForTodayLive(userId, todayTimestamp);
    }
    
    public LiveData<List<TaskEntity>> getTasksInTimeRange(int userId, long startTime, long endTime) {
        return taskDao.getTasksInTimeRangeLive(userId, startTime, endTime);
    }
    
    public Future<Long> insertTask(TaskEntity task) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> {
            long taskId = taskDao.insertTask(task);
            // Cập nhật task count cho category
            categoryDao.updateTaskCount(task.getCategoryId(), System.currentTimeMillis());
            return taskId;
        });
    }
    
    public void updateTask(TaskEntity task) {
        TodoDatabase.databaseWriteExecutor.execute(() -> taskDao.updateTask(task));
    }
    
    public void deleteTask(TaskEntity task) {
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            taskDao.deleteTask(task);
            // Cập nhật task count cho category
            categoryDao.updateTaskCount(task.getCategoryId(), System.currentTimeMillis());
        });
    }
    
    public void updateTaskCompletion(int taskId, boolean isCompleted) {
        TodoDatabase.databaseWriteExecutor.execute(() -> 
            taskDao.updateTaskCompletion(taskId, isCompleted, System.currentTimeMillis()));
    }
    
    // ==================== STATISTICS ====================
    
    public Future<Integer> getTotalTaskCount(int userId) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> taskDao.getTotalTaskCount(userId));
    }
    
    public Future<Integer> getCompletedTaskCount(int userId) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> taskDao.getCompletedTaskCount(userId));
    }
    
    public Future<Integer> getPendingTaskCount(int userId) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> taskDao.getPendingTaskCount(userId));
    }
    
    // ==================== SYNC METHODS (for immediate results) ====================
    
    public Future<List<TaskEntity>> getAllTasksByUserSync(int userId) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> taskDao.getAllTasksByUser(userId));
    }
    
    public Future<List<CategoryEntity>> getAllCategoriesByUserSync(int userId) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> categoryDao.getAllCategoriesByUser(userId));
    }
    
    public Future<List<TaskEntity>> searchTasksSync(int userId, String query) {
        return TodoDatabase.databaseWriteExecutor.submit(() -> taskDao.searchTasks(userId, query));
    }
}
