<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="?attr/colorSurfaceVariant" />
            <stroke
                android:width="2dp"
                android:color="?attr/colorPrimary" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/surface_color" />
            <stroke
                android:width="1dp"
                android:color="@color/card_stroke_color" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
