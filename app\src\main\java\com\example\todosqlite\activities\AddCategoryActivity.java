package com.example.todosqlite.activities;

import android.graphics.Color;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.example.todosqlite.R;
import com.example.todosqlite.database.CategoryDAO;
import com.example.todosqlite.models.Category;

/**
 * Activity để thêm danh mục mới
 */
public class AddCategoryActivity extends AppCompatActivity {
    
    private EditText etName, etDescription;
    private View viewSelectedColor;
    private Button btnSave, btnCancel;
    
    private CategoryDAO categoryDAO;
    private String selectedColor = "#2196F3"; // Default blue color
    
    // Predefined colors
    private String[] colors = {
        "#2196F3", "#4CAF50", "#FF9800", "#F44336", 
        "#9C27B0", "#607D8B", "#795548", "#E91E63"
    };
    
    private View[] colorViews;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_category);
        
        initViews();
        setupToolbar();
        setupColorPicker();
        setupClickListeners();
        
        categoryDAO = new CategoryDAO(this);
    }
    
    private void initViews() {
        etName = findViewById(R.id.et_category_name);
        etDescription = findViewById(R.id.et_category_description);
        viewSelectedColor = findViewById(R.id.view_selected_color);
        btnSave = findViewById(R.id.btn_save);
        btnCancel = findViewById(R.id.btn_cancel);
        
        // Color picker views
        colorViews = new View[8];
        colorViews[0] = findViewById(R.id.color_1);
        colorViews[1] = findViewById(R.id.color_2);
        colorViews[2] = findViewById(R.id.color_3);
        colorViews[3] = findViewById(R.id.color_4);
        colorViews[4] = findViewById(R.id.color_5);
        colorViews[5] = findViewById(R.id.color_6);
        colorViews[6] = findViewById(R.id.color_7);
        colorViews[7] = findViewById(R.id.color_8);
    }
    
    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Thêm danh mục");
        }
    }
    
    private void setupColorPicker() {
        // Set colors for color picker
        for (int i = 0; i < colorViews.length; i++) {
            colorViews[i].setBackgroundColor(Color.parseColor(colors[i]));
            
            final int index = i;
            colorViews[i].setOnClickListener(v -> {
                selectedColor = colors[index];
                updateSelectedColor();
                updateColorSelection(index);
            });
        }
        
        // Set default selected color
        updateSelectedColor();
        updateColorSelection(0);
    }
    
    private void updateSelectedColor() {
        viewSelectedColor.setBackgroundColor(Color.parseColor(selectedColor));
    }
    
    private void updateColorSelection(int selectedIndex) {
        for (int i = 0; i < colorViews.length; i++) {
            if (i == selectedIndex) {
                colorViews[i].setScaleX(1.2f);
                colorViews[i].setScaleY(1.2f);
                colorViews[i].setElevation(8f);
            } else {
                colorViews[i].setScaleX(1.0f);
                colorViews[i].setScaleY(1.0f);
                colorViews[i].setElevation(2f);
            }
        }
    }
    
    private void setupClickListeners() {
        btnSave.setOnClickListener(v -> saveCategory());
        btnCancel.setOnClickListener(v -> finish());
    }
    
    private void saveCategory() {
        String name = etName.getText().toString().trim();
        String description = etDescription.getText().toString().trim();
        
        if (name.isEmpty()) {
            etName.setError("Vui lòng nhập tên danh mục");
            etName.requestFocus();
            return;
        }
        
        // Create new category
        Category category = new Category(name, description, selectedColor, "folder", 1); // User ID = 1
        
        // Save to database
        long categoryId = categoryDAO.insertCategory(category);
        
        if (categoryId > 0) {
            Toast.makeText(this, "Đã thêm danh mục", Toast.LENGTH_SHORT).show();
            finish();
        } else {
            Toast.makeText(this, "Lỗi khi thêm danh mục", Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
