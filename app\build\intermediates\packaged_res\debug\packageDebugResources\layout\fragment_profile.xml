<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".fragments.ProfileFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- User Info Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center_vertical">

                <!-- Avatar -->
                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@mipmap/ic_launcher_round"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/circle_background" />

                <!-- User Details -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_user_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Người dùng mặc định"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorOnSurface" />

                    <TextView
                        android:id="@+id/tv_user_email"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Statistics Title -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Thống kê"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface"
            android:layout_marginBottom="12dp" />

        <!-- Statistics Cards -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Total Tasks -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_total_tasks"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorPrimary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tổng số"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Completed Tasks -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_completed_tasks"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/priority_low" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Hoàn thành"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Pending Tasks -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_pending_tasks"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/priority_medium" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Đang làm"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- Settings Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cài đặt"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="12dp" />

        <!-- Settings Options -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Edit Profile -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_person"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Chỉnh sửa hồ sơ"
                        android:textSize="16sp"
                        android:textColor="?attr/colorOnSurface" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_forward" />

                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider_color"
                    android:layout_marginStart="56dp" />

                <!-- Notifications -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_notifications"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Thông báo"
                        android:textSize="16sp"
                        android:textColor="?attr/colorOnSurface" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_forward" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
