package com.example.todosqlite.database;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.example.todosqlite.models.Task;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Data Access Object cho Task
 */
public class TaskDAO {
    private DatabaseHelper dbHelper;
    
    public TaskDAO(Context context) {
        dbHelper = DatabaseHelper.getInstance(context);
    }
    
    // Thêm task mới
    public long insertTask(Task task) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(DatabaseHelper.COLUMN_TITLE, task.getTitle());
        values.put(DatabaseHelper.COLUMN_DESCRIPTION, task.getDescription());
        values.put(DatabaseHelper.COLUMN_START_TIME, task.getStartTime() != null ? task.getStartTime().getTime() : null);
        values.put(DatabaseHelper.COLUMN_END_TIME, task.getEndTime() != null ? task.getEndTime().getTime() : null);
        values.put(DatabaseHelper.COLUMN_IS_COMPLETED, task.isCompleted() ? 1 : 0);
        values.put(DatabaseHelper.COLUMN_PRIORITY, task.getPriority());
        values.put(DatabaseHelper.COLUMN_CATEGORY_ID, task.getCategoryId());
        values.put(DatabaseHelper.COLUMN_USER_ID, task.getUserId());
        values.put(DatabaseHelper.COLUMN_CREATED_AT, task.getCreatedAt().getTime());
        values.put(DatabaseHelper.COLUMN_UPDATED_AT, task.getUpdatedAt().getTime());
        
        long id = db.insert(DatabaseHelper.TABLE_TASKS, null, values);
        db.close();
        return id;
    }
    
    // Cập nhật task
    public int updateTask(Task task) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(DatabaseHelper.COLUMN_TITLE, task.getTitle());
        values.put(DatabaseHelper.COLUMN_DESCRIPTION, task.getDescription());
        values.put(DatabaseHelper.COLUMN_START_TIME, task.getStartTime() != null ? task.getStartTime().getTime() : null);
        values.put(DatabaseHelper.COLUMN_END_TIME, task.getEndTime() != null ? task.getEndTime().getTime() : null);
        values.put(DatabaseHelper.COLUMN_IS_COMPLETED, task.isCompleted() ? 1 : 0);
        values.put(DatabaseHelper.COLUMN_PRIORITY, task.getPriority());
        values.put(DatabaseHelper.COLUMN_CATEGORY_ID, task.getCategoryId());
        values.put(DatabaseHelper.COLUMN_UPDATED_AT, new Date().getTime());
        
        int rowsAffected = db.update(DatabaseHelper.TABLE_TASKS, values, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(task.getId())});
        db.close();
        return rowsAffected;
    }
    
    // Xóa task
    public int deleteTask(int taskId) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        int rowsAffected = db.delete(DatabaseHelper.TABLE_TASKS, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(taskId)});
        db.close();
        return rowsAffected;
    }
    
    // Lấy task theo ID
    public Task getTaskById(int taskId) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        Cursor cursor = db.query(DatabaseHelper.TABLE_TASKS, null, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(taskId)}, 
            null, null, null);
        
        Task task = null;
        if (cursor.moveToFirst()) {
            task = cursorToTask(cursor);
        }
        cursor.close();
        db.close();
        return task;
    }
    
    // Lấy tất cả task của user
    public List<Task> getAllTasksByUser(int userId) {
        List<Task> tasks = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        Cursor cursor = db.query(DatabaseHelper.TABLE_TASKS, null, 
            DatabaseHelper.COLUMN_USER_ID + " = ?", new String[]{String.valueOf(userId)}, 
            null, null, DatabaseHelper.COLUMN_CREATED_AT + " DESC");
        
        if (cursor.moveToFirst()) {
            do {
                tasks.add(cursorToTask(cursor));
            } while (cursor.moveToNext());
        }
        cursor.close();
        db.close();
        return tasks;
    }
    
    // Lấy task theo category
    public List<Task> getTasksByCategory(int categoryId) {
        List<Task> tasks = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        Cursor cursor = db.query(DatabaseHelper.TABLE_TASKS, null, 
            DatabaseHelper.COLUMN_CATEGORY_ID + " = ?", new String[]{String.valueOf(categoryId)}, 
            null, null, DatabaseHelper.COLUMN_CREATED_AT + " DESC");
        
        if (cursor.moveToFirst()) {
            do {
                tasks.add(cursorToTask(cursor));
            } while (cursor.moveToNext());
        }
        cursor.close();
        db.close();
        return tasks;
    }
    
    // Lấy task đã hoàn thành
    public List<Task> getCompletedTasks(int userId) {
        List<Task> tasks = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        Cursor cursor = db.query(DatabaseHelper.TABLE_TASKS, null, 
            DatabaseHelper.COLUMN_USER_ID + " = ? AND " + DatabaseHelper.COLUMN_IS_COMPLETED + " = 1", 
            new String[]{String.valueOf(userId)}, 
            null, null, DatabaseHelper.COLUMN_UPDATED_AT + " DESC");
        
        if (cursor.moveToFirst()) {
            do {
                tasks.add(cursorToTask(cursor));
            } while (cursor.moveToNext());
        }
        cursor.close();
        db.close();
        return tasks;
    }
    
    // Tìm kiếm task
    public List<Task> searchTasks(int userId, String query) {
        List<Task> tasks = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        String selection = DatabaseHelper.COLUMN_USER_ID + " = ? AND (" +
            DatabaseHelper.COLUMN_TITLE + " LIKE ? OR " +
            DatabaseHelper.COLUMN_DESCRIPTION + " LIKE ?)";
        String[] selectionArgs = {String.valueOf(userId), "%" + query + "%", "%" + query + "%"};
        
        Cursor cursor = db.query(DatabaseHelper.TABLE_TASKS, null, selection, selectionArgs, 
            null, null, DatabaseHelper.COLUMN_CREATED_AT + " DESC");
        
        if (cursor.moveToFirst()) {
            do {
                tasks.add(cursorToTask(cursor));
            } while (cursor.moveToNext());
        }
        cursor.close();
        db.close();
        return tasks;
    }
    
    // Chuyển đổi Cursor thành Task object
    private Task cursorToTask(Cursor cursor) {
        Task task = new Task();
        task.setId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_ID)));
        task.setTitle(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_TITLE)));
        task.setDescription(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_DESCRIPTION)));
        
        long startTime = cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_START_TIME));
        if (startTime > 0) {
            task.setStartTime(new Date(startTime));
        }
        
        long endTime = cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_END_TIME));
        if (endTime > 0) {
            task.setEndTime(new Date(endTime));
        }
        
        task.setCompleted(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_IS_COMPLETED)) == 1);
        task.setPriority(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_PRIORITY)));
        task.setCategoryId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_CATEGORY_ID)));
        task.setUserId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_USER_ID)));
        task.setCreatedAt(new Date(cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_CREATED_AT))));
        task.setUpdatedAt(new Date(cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_UPDATED_AT))));
        
        return task;
    }
}
