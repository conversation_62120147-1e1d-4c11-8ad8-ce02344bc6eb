package com.example.todosqlite.database;

import android.content.Context;

import com.example.todosqlite.database.entities.TaskEntity;
import com.example.todosqlite.models.Task;
import com.example.todosqlite.utils.EntityConverter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * Data Access Object cho Task - Room wrapper để maintain compatibility
 */
public class TaskDAO {
    private TodoDatabase database;
    
    public TaskDAO(Context context) {
        database = TodoDatabase.getDatabase(context);
    }
    
    // Thêm task mới
    public long insertTask(Task task) {
        try {
            TaskEntity entity = EntityConverter.toTaskEntity(task);
            return database.taskDao().insertTask(entity);
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }
    
    // Cập nhật task
    public int updateTask(Task task) {
        try {
            TaskEntity entity = EntityConverter.toTaskEntity(task);
            database.taskDao().updateTask(entity);
            return 1; // Room doesn't return affected rows count
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    // Xóa task
    public int deleteTask(int taskId) {
        try {
            database.taskDao().deleteTaskById(taskId);
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    // Lấy task theo ID
    public Task getTaskById(int taskId) {
        try {
            TaskEntity entity = database.taskDao().getTaskById(taskId);
            return EntityConverter.toTask(entity);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    // Lấy tất cả task của user
    public List<Task> getAllTasksByUser(int userId) {
        try {
            List<TaskEntity> entities = database.taskDao().getAllTasksByUser(userId);
            return EntityConverter.toTaskList(entities);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    // Lấy task theo category
    public List<Task> getTasksByCategory(int categoryId) {
        try {
            List<TaskEntity> entities = database.taskDao().getTasksByCategory(categoryId);
            return EntityConverter.toTaskList(entities);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    // Lấy task đã hoàn thành
    public List<Task> getCompletedTasks(int userId) {
        try {
            List<TaskEntity> entities = database.taskDao().getCompletedTasks(userId);
            return EntityConverter.toTaskList(entities);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    // Tìm kiếm task
    public List<Task> searchTasks(int userId, String query) {
        try {
            List<TaskEntity> entities = database.taskDao().searchTasks(userId, query);
            return EntityConverter.toTaskList(entities);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
}
