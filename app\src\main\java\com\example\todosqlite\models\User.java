package com.example.todosqlite.models;

import java.util.Date;

/**
 * Model class cho User (Người dùng)
 */
public class User {
    private int id;
    private String username;        // Tên đăng nhập
    private String email;           // Email
    private String fullName;        // H<PERSON> tên đầy đủ
    private String avatar;          // Đường dẫn avatar
    private String phone;           // Số điện thoại
    private Date dateOfBirth;       // Ngày sinh
    private String gender;          // Giới tính
    private Date createdAt;         // Thời gian tạo tài khoản
    private Date updatedAt;         // Thời gian cập nhật
    private Date lastLoginAt;       // Lần đăng nhập cuối
    private boolean isActive;       // Trạng thái hoạt động

    // Constructor mặc định
    public User() {
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.isActive = true;
    }

    // Constructor với thông tin cơ bản
    public User(String username, String email, String fullName) {
        this();
        this.username = username;
        this.email = email;
        this.fullName = fullName;
    }

    // Constructor đầy đủ
    public User(String username, String email, String fullName, String phone, 
                Date dateOfBirth, String gender) {
        this();
        this.username = username;
        this.email = email;
        this.fullName = fullName;
        this.phone = phone;
        this.dateOfBirth = dateOfBirth;
        this.gender = gender;
    }

    // Getters và Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
        this.updatedAt = new Date();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
        this.updatedAt = new Date();
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
        this.updatedAt = new Date();
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
        this.updatedAt = new Date();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
        this.updatedAt = new Date();
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
        this.updatedAt = new Date();
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
        this.updatedAt = new Date();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getLastLoginAt() {
        return lastLoginAt;
    }

    public void setLastLoginAt(Date lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
        this.updatedAt = new Date();
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", fullName='" + fullName + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
