package com.example.todosqlite.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import java.util.Date;

/**
 * Room Entity cho SubTask
 */
@Entity(
    tableName = "subtasks",
    foreignKeys = @ForeignKey(
        entity = TaskEntity.class,
        parentColumns = "id",
        childColumns = "parent_task_id",
        onDelete = ForeignKey.CASCADE
    ),
    indices = {@Index("parent_task_id")}
)
public class SubTaskEntity {
    
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    public int id;
    
    @ColumnInfo(name = "title")
    public String title;
    
    @ColumnInfo(name = "description")
    public String description;
    
    @ColumnInfo(name = "is_completed")
    public boolean isCompleted;
    
    @ColumnInfo(name = "parent_task_id")
    public int parentTaskId;
    
    @ColumnInfo(name = "order_index")
    public int order;
    
    @ColumnInfo(name = "created_at")
    public Long createdAt;
    
    @ColumnInfo(name = "updated_at")
    public Long updatedAt;
    
    // Constructor mặc định
    public SubTaskEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.isCompleted = false;
        this.order = 0;
    }
    
    // Constructor với title
    public SubTaskEntity(String title, int parentTaskId) {
        this();
        this.title = title;
        this.parentTaskId = parentTaskId;
    }
    
    // Getters và Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = System.currentTimeMillis();
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        isCompleted = completed;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getParentTaskId() {
        return parentTaskId;
    }

    public void setParentTaskId(int parentTaskId) {
        this.parentTaskId = parentTaskId;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
        this.updatedAt = System.currentTimeMillis();
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Helper methods
    public Date getCreatedAtAsDate() {
        return createdAt != null ? new Date(createdAt) : null;
    }
    
    public Date getUpdatedAtAsDate() {
        return updatedAt != null ? new Date(updatedAt) : null;
    }
}
