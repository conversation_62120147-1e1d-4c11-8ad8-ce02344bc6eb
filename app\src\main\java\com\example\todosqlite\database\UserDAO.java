package com.example.todosqlite.database;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.example.todosqlite.models.User;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Data Access Object cho User
 */
public class UserDAO {
    private DatabaseHelper dbHelper;
    
    public UserDAO(Context context) {
        dbHelper = DatabaseHelper.getInstance(context);
    }
    
    // Thêm user mới
    public long insertUser(User user) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(DatabaseHelper.COLUMN_USERNAME, user.getUsername());
        values.put(DatabaseHelper.COLUMN_EMAIL, user.getEmail());
        values.put(DatabaseHelper.COLUMN_FULL_NAME, user.getFullName());
        values.put(DatabaseHelper.COLUMN_AVATAR, user.getAvatar());
        values.put(DatabaseHelper.COLUMN_PHONE, user.getPhone());
        values.put(DatabaseHelper.COLUMN_DATE_OF_BIRTH, user.getDateOfBirth() != null ? user.getDateOfBirth().getTime() : null);
        values.put(DatabaseHelper.COLUMN_GENDER, user.getGender());
        values.put(DatabaseHelper.COLUMN_LAST_LOGIN_AT, user.getLastLoginAt() != null ? user.getLastLoginAt().getTime() : null);
        values.put(DatabaseHelper.COLUMN_IS_ACTIVE, user.isActive() ? 1 : 0);
        values.put(DatabaseHelper.COLUMN_CREATED_AT, user.getCreatedAt().getTime());
        values.put(DatabaseHelper.COLUMN_UPDATED_AT, user.getUpdatedAt().getTime());
        
        long id = db.insert(DatabaseHelper.TABLE_USERS, null, values);
        db.close();
        return id;
    }
    
    // Cập nhật user
    public int updateUser(User user) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(DatabaseHelper.COLUMN_USERNAME, user.getUsername());
        values.put(DatabaseHelper.COLUMN_EMAIL, user.getEmail());
        values.put(DatabaseHelper.COLUMN_FULL_NAME, user.getFullName());
        values.put(DatabaseHelper.COLUMN_AVATAR, user.getAvatar());
        values.put(DatabaseHelper.COLUMN_PHONE, user.getPhone());
        values.put(DatabaseHelper.COLUMN_DATE_OF_BIRTH, user.getDateOfBirth() != null ? user.getDateOfBirth().getTime() : null);
        values.put(DatabaseHelper.COLUMN_GENDER, user.getGender());
        values.put(DatabaseHelper.COLUMN_IS_ACTIVE, user.isActive() ? 1 : 0);
        values.put(DatabaseHelper.COLUMN_UPDATED_AT, new Date().getTime());
        
        int rowsAffected = db.update(DatabaseHelper.TABLE_USERS, values, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(user.getId())});
        db.close();
        return rowsAffected;
    }
    
    // Cập nhật last login
    public void updateLastLogin(int userId) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        long currentTime = new Date().getTime();
        values.put(DatabaseHelper.COLUMN_LAST_LOGIN_AT, currentTime);
        values.put(DatabaseHelper.COLUMN_UPDATED_AT, currentTime);
        
        db.update(DatabaseHelper.TABLE_USERS, values, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(userId)});
        db.close();
    }
    
    // Xóa user
    public int deleteUser(int userId) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        int rowsAffected = db.delete(DatabaseHelper.TABLE_USERS, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(userId)});
        db.close();
        return rowsAffected;
    }
    
    // Lấy user theo ID
    public User getUserById(int userId) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        Cursor cursor = db.query(DatabaseHelper.TABLE_USERS, null, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(userId)}, 
            null, null, null);
        
        User user = null;
        if (cursor.moveToFirst()) {
            user = cursorToUser(cursor);
        }
        cursor.close();
        db.close();
        return user;
    }
    
    // Lấy user theo username
    public User getUserByUsername(String username) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        Cursor cursor = db.query(DatabaseHelper.TABLE_USERS, null, 
            DatabaseHelper.COLUMN_USERNAME + " = ?", new String[]{username}, 
            null, null, null);
        
        User user = null;
        if (cursor.moveToFirst()) {
            user = cursorToUser(cursor);
        }
        cursor.close();
        db.close();
        return user;
    }
    
    // Lấy user theo email
    public User getUserByEmail(String email) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        Cursor cursor = db.query(DatabaseHelper.TABLE_USERS, null, 
            DatabaseHelper.COLUMN_EMAIL + " = ?", new String[]{email}, 
            null, null, null);
        
        User user = null;
        if (cursor.moveToFirst()) {
            user = cursorToUser(cursor);
        }
        cursor.close();
        db.close();
        return user;
    }
    
    // Lấy tất cả user
    public List<User> getAllUsers() {
        List<User> users = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        Cursor cursor = db.query(DatabaseHelper.TABLE_USERS, null, null, null, 
            null, null, DatabaseHelper.COLUMN_CREATED_AT + " DESC");
        
        if (cursor.moveToFirst()) {
            do {
                users.add(cursorToUser(cursor));
            } while (cursor.moveToNext());
        }
        cursor.close();
        db.close();
        return users;
    }
    
    // Lấy user đang hoạt động
    public List<User> getActiveUsers() {
        List<User> users = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        Cursor cursor = db.query(DatabaseHelper.TABLE_USERS, null, 
            DatabaseHelper.COLUMN_IS_ACTIVE + " = 1", null, 
            null, null, DatabaseHelper.COLUMN_LAST_LOGIN_AT + " DESC");
        
        if (cursor.moveToFirst()) {
            do {
                users.add(cursorToUser(cursor));
            } while (cursor.moveToNext());
        }
        cursor.close();
        db.close();
        return users;
    }
    
    // Chuyển đổi Cursor thành User object
    private User cursorToUser(Cursor cursor) {
        User user = new User();
        user.setId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_ID)));
        user.setUsername(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_USERNAME)));
        user.setEmail(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_EMAIL)));
        user.setFullName(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_FULL_NAME)));
        user.setAvatar(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_AVATAR)));
        user.setPhone(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_PHONE)));
        
        long dateOfBirth = cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_DATE_OF_BIRTH));
        if (dateOfBirth > 0) {
            user.setDateOfBirth(new Date(dateOfBirth));
        }
        
        user.setGender(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_GENDER)));
        
        long lastLoginAt = cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_LAST_LOGIN_AT));
        if (lastLoginAt > 0) {
            user.setLastLoginAt(new Date(lastLoginAt));
        }
        
        user.setActive(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_IS_ACTIVE)) == 1);
        user.setCreatedAt(new Date(cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_CREATED_AT))));
        user.setUpdatedAt(new Date(cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_UPDATED_AT))));
        
        return user;
    }
}
