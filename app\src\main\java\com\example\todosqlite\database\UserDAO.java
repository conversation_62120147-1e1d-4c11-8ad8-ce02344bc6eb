package com.example.todosqlite.database;

import android.content.Context;

import com.example.todosqlite.database.entities.UserEntity;
import com.example.todosqlite.models.User;
import com.example.todosqlite.utils.EntityConverter;

import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object cho User - Room wrapper để maintain compatibility
 */
public class UserDAO {
    private TodoDatabase database;

    public UserDAO(Context context) {
        database = TodoDatabase.getDatabase(context);
    }
    
    // Thêm user mới
    public long insertUser(User user) {
        try {
            UserEntity entity = EntityConverter.toUserEntity(user);
            return database.userDao().insertUser(entity);
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    // Lấy user theo ID
    public User getUserById(int userId) {
        try {
            UserEntity entity = database.userDao().getUserById(userId);
            return EntityConverter.toUser(entity);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // Cập nhật user
    public int updateUser(User user) {
        try {
            UserEntity entity = EntityConverter.toUserEntity(user);
            database.userDao().updateUser(entity);
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
