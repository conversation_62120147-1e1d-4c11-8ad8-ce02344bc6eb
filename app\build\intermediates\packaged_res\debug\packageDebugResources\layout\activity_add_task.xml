<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Task Title -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Tiêu đề nhiệm vụ"
                app:boxStrokeColor="?attr/colorPrimary"
                app:hintTextColor="?attr/colorPrimary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_task_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="2" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Task Description -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Mô tả nhiệm vụ"
                app:boxStrokeColor="?attr/colorPrimary"
                app:hintTextColor="?attr/colorPrimary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_task_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:lines="3"
                    android:gravity="top" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Category Selection -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Danh mục"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <Spinner
                android:id="@+id/spinner_category"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/spinner_background" />

            <!-- Priority Selection -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Độ ưu tiên"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <Spinner
                android:id="@+id/spinner_priority"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/spinner_background" />

            <!-- Time Selection -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Thời gian"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <!-- Start Time -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Bắt đầu:"
                    android:textSize="14sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:gravity="center_vertical" />

                <TextView
                    android:id="@+id/tv_start_time"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="2"
                    android:text="Chọn thời gian"
                    android:textSize="14sp"
                    android:textColor="?attr/colorOnSurface"
                    android:background="@drawable/time_selector_background"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="12dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:drawableEnd="@drawable/ic_time"
                    android:drawablePadding="8dp" />

            </LinearLayout>

            <!-- End Time -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="24dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Kết thúc:"
                    android:textSize="14sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:gravity="center_vertical" />

                <TextView
                    android:id="@+id/tv_end_time"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="2"
                    android:text="Chọn thời gian"
                    android:textSize="14sp"
                    android:textColor="?attr/colorOnSurface"
                    android:background="@drawable/time_selector_background"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="12dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:drawableEnd="@drawable/ic_time"
                    android:drawablePadding="8dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/surface_color"
        android:elevation="4dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Hủy"
            android:textColor="?attr/colorOnSurface"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Lưu"
            android:textColor="@android:color/white"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

</LinearLayout>
