package com.example.todosqlite.activities;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.example.todosqlite.R;
import com.example.todosqlite.database.CategoryDAO;
import com.example.todosqlite.database.TaskDAO;
import com.example.todosqlite.models.Category;
import com.example.todosqlite.models.Task;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Activity để thêm nhiệm vụ mới
 */
public class AddTaskActivity extends AppCompatActivity {
    
    private EditText etTitle, etDescription;
    private TextView tvStartTime, tvEndTime;
    private Spinner spinnerCategory, spinnerPriority;
    private Button btnSave, btnCancel;
    
    private TaskDAO taskDAO;
    private CategoryDAO categoryDAO;
    private List<Category> categoryList;
    private Date startTime, endTime;
    
    private SimpleDateFormat dateTimeFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_task);
        
        initViews();
        setupToolbar();
        setupSpinners();
        setupClickListeners();
        
        taskDAO = new TaskDAO(this);
        categoryDAO = new CategoryDAO(this);
        loadCategories();
    }
    
    private void initViews() {
        etTitle = findViewById(R.id.et_task_title);
        etDescription = findViewById(R.id.et_task_description);
        tvStartTime = findViewById(R.id.tv_start_time);
        tvEndTime = findViewById(R.id.tv_end_time);
        spinnerCategory = findViewById(R.id.spinner_category);
        spinnerPriority = findViewById(R.id.spinner_priority);
        btnSave = findViewById(R.id.btn_save);
        btnCancel = findViewById(R.id.btn_cancel);
    }
    
    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Thêm nhiệm vụ");
        }
    }
    
    private void setupSpinners() {
        // Priority spinner
        String[] priorities = {"Thấp", "Trung bình", "Cao"};
        ArrayAdapter<String> priorityAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, priorities);
        priorityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerPriority.setAdapter(priorityAdapter);
    }
    
    private void loadCategories() {
        categoryList = categoryDAO.getAllCategoriesByUser(1); // User ID = 1
        
        List<String> categoryNames = new ArrayList<>();
        for (Category category : categoryList) {
            categoryNames.add(category.getName());
        }
        
        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(this,
            android.R.layout.simple_spinner_item, categoryNames);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategory.setAdapter(categoryAdapter);
    }
    
    private void setupClickListeners() {
        tvStartTime.setOnClickListener(v -> showDateTimePicker(true));
        tvEndTime.setOnClickListener(v -> showDateTimePicker(false));
        
        btnSave.setOnClickListener(v -> saveTask());
        btnCancel.setOnClickListener(v -> finish());
    }
    
    private void showDateTimePicker(boolean isStartTime) {
        Calendar calendar = Calendar.getInstance();
        
        DatePickerDialog datePickerDialog = new DatePickerDialog(this,
            (view, year, month, dayOfMonth) -> {
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.MONTH, month);
                calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
                
                TimePickerDialog timePickerDialog = new TimePickerDialog(this,
                    (timeView, hourOfDay, minute) -> {
                        calendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
                        calendar.set(Calendar.MINUTE, minute);
                        
                        Date selectedDateTime = calendar.getTime();
                        String formattedDateTime = dateTimeFormat.format(selectedDateTime);
                        
                        if (isStartTime) {
                            startTime = selectedDateTime;
                            tvStartTime.setText(formattedDateTime);
                        } else {
                            endTime = selectedDateTime;
                            tvEndTime.setText(formattedDateTime);
                        }
                    }, calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), true);
                
                timePickerDialog.show();
            }, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH));
        
        datePickerDialog.show();
    }
    
    private void saveTask() {
        String title = etTitle.getText().toString().trim();
        String description = etDescription.getText().toString().trim();
        
        if (title.isEmpty()) {
            etTitle.setError("Vui lòng nhập tiêu đề");
            etTitle.requestFocus();
            return;
        }
        
        if (categoryList.isEmpty()) {
            Toast.makeText(this, "Vui lòng tạo danh mục trước", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Get selected category
        int selectedCategoryPosition = spinnerCategory.getSelectedItemPosition();
        Category selectedCategory = categoryList.get(selectedCategoryPosition);
        
        // Get selected priority (1-3)
        int priority = spinnerPriority.getSelectedItemPosition() + 1;
        
        // Create new task
        Task task = new Task(title, description, startTime, endTime, 
                           selectedCategory.getId(), 1); // User ID = 1
        task.setPriority(priority);
        
        // Save to database on background thread
        new Thread(() -> {
            try {
                long taskId = taskDAO.insertTask(task);

                if (taskId > 0) {
                    // Update category task count
                    categoryDAO.updateTaskCount(selectedCategory.getId());

                    // Show success message on UI thread
                    runOnUiThread(() -> {
                        Toast.makeText(this, "Đã thêm nhiệm vụ", Toast.LENGTH_SHORT).show();
                        finish();
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "Lỗi khi thêm nhiệm vụ", Toast.LENGTH_SHORT).show();
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "Lỗi khi thêm nhiệm vụ: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
