package com.example.todosqlite.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import java.util.Date;

/**
 * Room Entity cho User
 */
@Entity(tableName = "users")
public class UserEntity {
    
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    public int id;
    
    @ColumnInfo(name = "username")
    public String username;
    
    @ColumnInfo(name = "email")
    public String email;
    
    @ColumnInfo(name = "full_name")
    public String fullName;
    
    @ColumnInfo(name = "avatar")
    public String avatar;
    
    @ColumnInfo(name = "phone")
    public String phone;
    
    @ColumnInfo(name = "date_of_birth")
    public Long dateOfBirth; // Timestamp
    
    @ColumnInfo(name = "gender")
    public String gender;
    
    @ColumnInfo(name = "last_login_at")
    public Long lastLoginAt; // Timestamp
    
    @ColumnInfo(name = "is_active")
    public boolean isActive;
    
    @ColumnInfo(name = "created_at")
    public Long createdAt; // Timestamp
    
    @ColumnInfo(name = "updated_at")
    public Long updatedAt; // Timestamp
    
    // Constructor mặc định (required by Room)
    public UserEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.isActive = true;
    }
    
    // Constructor với thông tin cơ bản
    public UserEntity(String username, String email, String fullName) {
        this();
        this.username = username;
        this.email = email;
        this.fullName = fullName;
    }
    
    // Getters và Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
        this.updatedAt = System.currentTimeMillis();
    }

    public Long getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Long dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
        this.updatedAt = System.currentTimeMillis();
    }

    public Long getLastLoginAt() {
        return lastLoginAt;
    }

    public void setLastLoginAt(Long lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
        this.updatedAt = System.currentTimeMillis();
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
        this.updatedAt = System.currentTimeMillis();
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Helper methods để convert Date
    public Date getDateOfBirthAsDate() {
        return dateOfBirth != null ? new Date(dateOfBirth) : null;
    }
    
    public void setDateOfBirthFromDate(Date date) {
        this.dateOfBirth = date != null ? date.getTime() : null;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public Date getLastLoginAsDate() {
        return lastLoginAt != null ? new Date(lastLoginAt) : null;
    }
    
    public void setLastLoginFromDate(Date date) {
        this.lastLoginAt = date != null ? date.getTime() : null;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public Date getCreatedAtAsDate() {
        return createdAt != null ? new Date(createdAt) : null;
    }
    
    public Date getUpdatedAtAsDate() {
        return updatedAt != null ? new Date(updatedAt) : null;
    }
}
