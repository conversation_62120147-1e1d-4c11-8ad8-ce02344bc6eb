package com.example.todosqlite;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageButton;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.todosqlite.adapters.CategoryAdapter;
import com.example.todosqlite.database.CategoryDAO;
import com.example.todosqlite.fragments.CalendarFragment;
import com.example.todosqlite.fragments.ProfileFragment;
import com.example.todosqlite.fragments.TaskFragment;
import com.example.todosqlite.models.Category;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.navigation.NavigationView;

import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity
        implements NavigationView.OnNavigationItemSelectedListener {

    // Views
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private Toolbar toolbar;
    private BottomNavigationView bottomNavigation;
    private SearchView searchView;
    private RecyclerView rvCategories;
    private FloatingActionButton fabAddTask, fabAddCategory;
    private ImageButton btnMenu;

    // Fragments
    private TaskFragment taskFragment;
    private CalendarFragment calendarFragment;
    private ProfileFragment profileFragment;
    private Fragment currentFragment;

    // Adapters and Data
    private CategoryAdapter categoryAdapter;
    private CategoryDAO categoryDAO;
    private List<Category> categoryList;

    // Current selected category
    private int selectedCategoryId = -1; // -1 means all tasks

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initViews();
        setupToolbar();
        setupDrawer();
        setupBottomNavigation();
        setupCategoryRecyclerView();
        setupFabButtons();
        setupSearchView();

        // Initialize data
        categoryDAO = new CategoryDAO(this);
        loadCategories();

        // Set default fragment
        if (savedInstanceState == null) {
            showTaskFragment();
            bottomNavigation.setSelectedItemId(R.id.nav_tasks);
        }
    }

    private void initViews() {
        drawerLayout = findViewById(R.id.drawer_layout);
        navigationView = findViewById(R.id.nav_view);
        toolbar = findViewById(R.id.toolbar);
        bottomNavigation = findViewById(R.id.bottom_navigation);
        searchView = findViewById(R.id.search_view);
        rvCategories = findViewById(R.id.rv_categories);
        fabAddTask = findViewById(R.id.fab_add_task);
        fabAddCategory = findViewById(R.id.fab_add_category);
        btnMenu = findViewById(R.id.btn_menu);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayShowTitleEnabled(false);
        }
    }

    private void setupDrawer() {
        ActionBarDrawerToggle toggle = new ActionBarDrawerToggle(
                this, drawerLayout, toolbar,
                R.string.navigation_drawer_open, R.string.navigation_drawer_close);
        drawerLayout.addDrawerListener(toggle);
        toggle.syncState();

        navigationView.setNavigationItemSelectedListener(this);

        // Menu button click listener
        btnMenu.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                drawerLayout.openDrawer(GravityCompat.START);
            }
        });
    }

    private void setupBottomNavigation() {
        bottomNavigation.setOnItemSelectedListener(new BottomNavigationView.OnItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                int itemId = item.getItemId();
                if (itemId == R.id.nav_tasks) {
                    showTaskFragment();
                    return true;
                } else if (itemId == R.id.nav_calendar) {
                    showCalendarFragment();
                    return true;
                } else if (itemId == R.id.nav_profile) {
                    showProfileFragment();
                    return true;
                }
                return false;
            }
        });
    }

    private void setupCategoryRecyclerView() {
        categoryList = new ArrayList<>();
        categoryAdapter = new CategoryAdapter(this, categoryList);
        rvCategories.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        rvCategories.setAdapter(categoryAdapter);

        categoryAdapter.setOnCategoryClickListener(new CategoryAdapter.OnCategoryClickListener() {
            @Override
            public void onCategoryClick(Category category) {
                selectedCategoryId = category.getId();
                categoryAdapter.setSelectedCategory(selectedCategoryId);

                // Filter tasks by category
                if (currentFragment instanceof TaskFragment) {
                    ((TaskFragment) currentFragment).filterTasksByCategory(selectedCategoryId);
                }
            }
        });
    }

    private void setupFabButtons() {
        fabAddTask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // TODO: Open Add Task Activity
                Intent intent = new Intent(MainActivity.this, AddTaskActivity.class);
                startActivity(intent);
            }
        });

        fabAddCategory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // TODO: Open Add Category Activity
                Intent intent = new Intent(MainActivity.this, AddCategoryActivity.class);
                startActivity(intent);
            }
        });
    }

    private void setupSearchView() {
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                performSearch(query);
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                performSearch(newText);
                return true;
            }
        });
    }

    private void performSearch(String query) {
        if (currentFragment instanceof TaskFragment) {
            ((TaskFragment) currentFragment).searchTasks(query);
        }
    }

    private void loadCategories() {
        categoryList.clear();
        categoryList.addAll(categoryDAO.getCategoriesWithTaskCount(1)); // User ID = 1
        categoryAdapter.notifyDataSetChanged();
    }

    // Fragment management methods
    private void showTaskFragment() {
        if (taskFragment == null) {
            taskFragment = TaskFragment.newInstance();
        }
        replaceFragment(taskFragment);
        currentFragment = taskFragment;
    }

    private void showCalendarFragment() {
        if (calendarFragment == null) {
            calendarFragment = CalendarFragment.newInstance();
        }
        replaceFragment(calendarFragment);
        currentFragment = calendarFragment;
    }

    private void showProfileFragment() {
        if (profileFragment == null) {
            profileFragment = ProfileFragment.newInstance();
        }
        replaceFragment(profileFragment);
        currentFragment = profileFragment;
    }

    private void replaceFragment(Fragment fragment) {
        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .commit();
    }

    // Navigation drawer item selection
    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if (itemId == R.id.nav_all_tasks) {
            selectedCategoryId = -1;
            if (currentFragment instanceof TaskFragment) {
                ((TaskFragment) currentFragment).filterTasksByCategory(-1);
            }
            bottomNavigation.setSelectedItemId(R.id.nav_tasks);
        } else if (itemId == R.id.nav_completed_tasks) {
            // TODO: Show completed tasks
        } else if (itemId == R.id.nav_pending_tasks) {
            // TODO: Show pending tasks
        } else if (itemId == R.id.nav_work) {
            // TODO: Filter by work category
        } else if (itemId == R.id.nav_personal) {
            // TODO: Filter by personal category
        } else if (itemId == R.id.nav_settings) {
            // TODO: Open settings
        } else if (itemId == R.id.nav_about) {
            // TODO: Show about dialog
        }

        drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Refresh data when returning to activity
        loadCategories();
        if (currentFragment instanceof TaskFragment) {
            ((TaskFragment) currentFragment).refreshTasks();
        } else if (currentFragment instanceof CalendarFragment) {
            ((CalendarFragment) currentFragment).refreshTasks();
        }
    }

}