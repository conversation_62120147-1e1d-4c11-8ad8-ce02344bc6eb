# 📍 Vị trí lưu trữ Database

## 🗄️ **Room Database Location**

### **Đường dẫn chính:**
```
📱 Android Device Internal Storage:
/data/data/com.example.todosqlite/databases/todo_database
```

### **Chi tiết cấu trúc:**
```
📁 /data/data/com.example.todosqlite/
├── 📁 databases/
│   ├── 📄 todo_database          # Main database file
│   ├── 📄 todo_database-shm      # Shared memory file (SQLite WAL mode)
│   └── 📄 todo_database-wal      # Write-Ahead Log file
├── 📁 shared_prefs/              # SharedPreferences files
├── 📁 cache/                     # App cache
└── 📁 files/                     # App private files
```

## 🔍 **Cách truy cập Database**

### **1. Android Studio Database Inspector:**
```
1. Mở Android Studio
2. Chạy app trên device/emulator
3. View → Tool Windows → App Inspection
4. Chọn tab "Database Inspector"
5. Chọn app "com.example.todosqlite"
6. Xem tables: users, categories, tasks, subtasks
```

### **2. ADB Commands:**
```bash
# Kết nối với device
adb shell

# Chuyển đến thư mục database
cd /data/data/com.example.todosqlite/databases/

# Liệt kê files
ls -la

# Mở SQLite CLI (nếu có)
sqlite3 todo_database

# Xem tables
.tables

# Query data
SELECT * FROM tasks;
```

### **3. Pull database file về máy tính:**
```bash
# Pull database file
adb pull /data/data/com.example.todosqlite/databases/todo_database ./

# Mở bằng SQLite browser trên máy tính
# Download: https://sqlitebrowser.org/
```

## 📊 **Database Schema**

### **Tables được tạo:**
1. **users** - Thông tin người dùng
2. **categories** - Danh mục nhiệm vụ  
3. **tasks** - Nhiệm vụ chính
4. **subtasks** - Nhiệm vụ con (mới thêm)

### **Relationships:**
```
users (1) ←→ (n) categories
categories (1) ←→ (n) tasks  
tasks (1) ←→ (n) subtasks
```

## ⚠️ **Lưu ý quan trọng**

### **Quyền truy cập:**
- Database chỉ accessible bởi app owner
- Cần root access để truy cập từ bên ngoài
- Emulator dễ truy cập hơn real device

### **Backup & Restore:**
```bash
# Backup
adb pull /data/data/com.example.todosqlite/databases/ ./backup/

# Restore (cần root)
adb push ./backup/todo_database /data/data/com.example.todosqlite/databases/
```

### **Development vs Production:**
- **Development**: Có thể xem qua Database Inspector
- **Production**: Database được encrypt và protect
- **Debug builds**: Dễ dàng debug và inspect

## 🛠️ **Tools để xem Database**

### **1. Android Studio Database Inspector** (Recommended)
- ✅ Real-time data viewing
- ✅ Query execution
- ✅ Schema inspection
- ✅ No setup required

### **2. SQLite Browser** (Desktop)
- ✅ Advanced querying
- ✅ Schema design
- ✅ Data export/import
- ❌ Cần pull file về máy

### **3. ADB + SQLite CLI**
- ✅ Command line access
- ✅ Scripting support
- ❌ Cần technical knowledge

## 📱 **App-specific paths**

```
Package: com.example.todosqlite
Database: todo_database
Size: ~50KB (with sample data)
Version: 1
Auto-backup: Disabled (local only)
```
