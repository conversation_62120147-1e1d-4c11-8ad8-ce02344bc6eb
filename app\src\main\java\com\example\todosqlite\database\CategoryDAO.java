package com.example.todosqlite.database;

import android.content.Context;

import com.example.todosqlite.database.entities.CategoryEntity;
import com.example.todosqlite.models.Category;
import com.example.todosqlite.utils.EntityConverter;

import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object cho Category - Room wrapper để maintain compatibility
 */
public class CategoryDAO {
    private TodoDatabase database;

    public CategoryDAO(Context context) {
        database = TodoDatabase.getDatabase(context);
    }
    
    // Thêm category mới
    public long insertCategory(Category category) {
        try {
            CategoryEntity entity = EntityConverter.toCategoryEntity(category);
            return database.categoryDao().insertCategory(entity);
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }
    
    // Cập nhật category
    public int updateCategory(Category category) {
        try {
            CategoryEntity entity = EntityConverter.toCategoryEntity(category);
            database.categoryDao().updateCategory(entity);
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    // Xóa category
    public int deleteCategory(int categoryId) {
        try {
            database.categoryDao().deleteCategoryById(categoryId);
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    // Lấy category theo ID
    public Category getCategoryById(int categoryId) {
        try {
            CategoryEntity entity = database.categoryDao().getCategoryById(categoryId);
            return EntityConverter.toCategory(entity);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    // Lấy tất cả category của user
    public List<Category> getAllCategoriesByUser(int userId) {
        try {
            List<CategoryEntity> entities = database.categoryDao().getAllCategoriesByUser(userId);
            return EntityConverter.toCategoryList(entities);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    // Lấy category với task count được cập nhật
    public List<Category> getCategoriesWithTaskCount(int userId) {
        try {
            List<CategoryEntity> entities = database.categoryDao().getCategoriesWithTaskCount(userId);
            List<Category> categories = EntityConverter.toCategoryList(entities);

            // Thêm danh mục "Tất cả" vào đầu danh sách
            Category allCategory = new Category("Tất cả", "Tất cả các nhiệm vụ", "#2196F3", "all_inclusive", userId);
            allCategory.setId(-1); // ID đặc biệt cho "Tất cả"

            // Đếm tổng số task của user
            int totalTasks = 0;
            for (Category category : categories) {
                totalTasks += category.getTaskCount();
            }
            allCategory.setTaskCount(totalTasks);

            // Thêm vào đầu danh sách
            categories.add(0, allCategory);

            return categories;
        } catch (Exception e) {
            e.printStackTrace();
            // Trả về ít nhất danh mục "Tất cả"
            List<Category> defaultList = new ArrayList<>();
            Category allCategory = new Category("Tất cả", "Tất cả các nhiệm vụ", "#2196F3", "all_inclusive", userId);
            allCategory.setId(-1);
            allCategory.setTaskCount(0);
            defaultList.add(allCategory);
            return defaultList;
        }
    }

    // Cập nhật số lượng task trong category
    public void updateTaskCount(int categoryId) {
        try {
            database.categoryDao().updateTaskCount(categoryId, System.currentTimeMillis());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
