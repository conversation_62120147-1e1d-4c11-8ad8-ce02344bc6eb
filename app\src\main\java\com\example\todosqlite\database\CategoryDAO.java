package com.example.todosqlite.database;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.example.todosqlite.models.Category;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Data Access Object cho Category
 */
public class CategoryDAO {
    private DatabaseHelper dbHelper;
    
    public CategoryDAO(Context context) {
        dbHelper = DatabaseHelper.getInstance(context);
    }
    
    // Thêm category mới
    public long insertCategory(Category category) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(DatabaseHelper.COLUMN_NAME, category.getName());
        values.put(DatabaseHelper.COLUMN_DESCRIPTION, category.getDescription());
        values.put(DatabaseHelper.COLUMN_COLOR, category.getColor());
        values.put(DatabaseHelper.COLUMN_ICON, category.getIcon());
        values.put(DatabaseHelper.COLUMN_USER_ID, category.getUserId());
        values.put(DatabaseHelper.COLUMN_TASK_COUNT, category.getTaskCount());
        values.put(DatabaseHelper.COLUMN_CREATED_AT, category.getCreatedAt().getTime());
        values.put(DatabaseHelper.COLUMN_UPDATED_AT, category.getUpdatedAt().getTime());
        
        long id = db.insert(DatabaseHelper.TABLE_CATEGORIES, null, values);
        db.close();
        return id;
    }
    
    // Cập nhật category
    public int updateCategory(Category category) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(DatabaseHelper.COLUMN_NAME, category.getName());
        values.put(DatabaseHelper.COLUMN_DESCRIPTION, category.getDescription());
        values.put(DatabaseHelper.COLUMN_COLOR, category.getColor());
        values.put(DatabaseHelper.COLUMN_ICON, category.getIcon());
        values.put(DatabaseHelper.COLUMN_TASK_COUNT, category.getTaskCount());
        values.put(DatabaseHelper.COLUMN_UPDATED_AT, new Date().getTime());
        
        int rowsAffected = db.update(DatabaseHelper.TABLE_CATEGORIES, values, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(category.getId())});
        db.close();
        return rowsAffected;
    }
    
    // Xóa category
    public int deleteCategory(int categoryId) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        int rowsAffected = db.delete(DatabaseHelper.TABLE_CATEGORIES, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(categoryId)});
        db.close();
        return rowsAffected;
    }
    
    // Lấy category theo ID
    public Category getCategoryById(int categoryId) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        Cursor cursor = db.query(DatabaseHelper.TABLE_CATEGORIES, null, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(categoryId)}, 
            null, null, null);
        
        Category category = null;
        if (cursor.moveToFirst()) {
            category = cursorToCategory(cursor);
        }
        cursor.close();
        db.close();
        return category;
    }
    
    // Lấy tất cả category của user
    public List<Category> getAllCategoriesByUser(int userId) {
        List<Category> categories = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        Cursor cursor = db.query(DatabaseHelper.TABLE_CATEGORIES, null, 
            DatabaseHelper.COLUMN_USER_ID + " = ?", new String[]{String.valueOf(userId)}, 
            null, null, DatabaseHelper.COLUMN_CREATED_AT + " ASC");
        
        if (cursor.moveToFirst()) {
            do {
                categories.add(cursorToCategory(cursor));
            } while (cursor.moveToNext());
        }
        cursor.close();
        db.close();
        return categories;
    }
    
    // Cập nhật số lượng task trong category
    public void updateTaskCount(int categoryId) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        
        // Đếm số task trong category
        String countQuery = "SELECT COUNT(*) FROM " + DatabaseHelper.TABLE_TASKS + 
            " WHERE " + DatabaseHelper.COLUMN_CATEGORY_ID + " = ?";
        Cursor cursor = db.rawQuery(countQuery, new String[]{String.valueOf(categoryId)});
        
        int taskCount = 0;
        if (cursor.moveToFirst()) {
            taskCount = cursor.getInt(0);
        }
        cursor.close();
        
        // Cập nhật task count
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_TASK_COUNT, taskCount);
        values.put(DatabaseHelper.COLUMN_UPDATED_AT, new Date().getTime());
        
        db.update(DatabaseHelper.TABLE_CATEGORIES, values, 
            DatabaseHelper.COLUMN_ID + " = ?", new String[]{String.valueOf(categoryId)});
        db.close();
    }
    
    // Lấy category với task count được cập nhật
    public List<Category> getCategoriesWithTaskCount(int userId) {
        List<Category> categories = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        String query = "SELECT c.*, COUNT(t." + DatabaseHelper.COLUMN_ID + ") as task_count " +
            "FROM " + DatabaseHelper.TABLE_CATEGORIES + " c " +
            "LEFT JOIN " + DatabaseHelper.TABLE_TASKS + " t ON c." + DatabaseHelper.COLUMN_ID + 
            " = t." + DatabaseHelper.COLUMN_CATEGORY_ID + 
            " WHERE c." + DatabaseHelper.COLUMN_USER_ID + " = ? " +
            "GROUP BY c." + DatabaseHelper.COLUMN_ID + 
            " ORDER BY c." + DatabaseHelper.COLUMN_CREATED_AT + " ASC";
        
        Cursor cursor = db.rawQuery(query, new String[]{String.valueOf(userId)});
        
        if (cursor.moveToFirst()) {
            do {
                Category category = cursorToCategory(cursor);
                category.setTaskCount(cursor.getInt(cursor.getColumnIndexOrThrow("task_count")));
                categories.add(category);
            } while (cursor.moveToNext());
        }
        cursor.close();
        db.close();
        return categories;
    }
    
    // Chuyển đổi Cursor thành Category object
    private Category cursorToCategory(Cursor cursor) {
        Category category = new Category();
        category.setId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_ID)));
        category.setName(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_NAME)));
        category.setDescription(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_DESCRIPTION)));
        category.setColor(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_COLOR)));
        category.setIcon(cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_ICON)));
        category.setUserId(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_USER_ID)));
        category.setTaskCount(cursor.getInt(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_TASK_COUNT)));
        category.setCreatedAt(new Date(cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_CREATED_AT))));
        category.setUpdatedAt(new Date(cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_UPDATED_AT))));
        
        return category;
    }
}
