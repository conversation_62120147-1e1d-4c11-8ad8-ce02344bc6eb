package com.example.todosqlite.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import java.util.Date;

/**
 * Room Entity cho Task
 */
@Entity(
    tableName = "tasks",
    foreignKeys = {
        @ForeignKey(
            entity = CategoryEntity.class,
            parentColumns = "id",
            childColumns = "category_id",
            onDelete = ForeignKey.CASCADE
        ),
        @ForeignKey(
            entity = UserEntity.class,
            parentColumns = "id",
            childColumns = "user_id",
            onDelete = ForeignKey.CASCADE
        )
    },
    indices = {@Index("category_id"), @Index("user_id")}
)
public class TaskEntity {
    
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    public int id;
    
    @ColumnInfo(name = "title")
    public String title;
    
    @ColumnInfo(name = "description")
    public String description;
    
    @ColumnInfo(name = "start_time")
    public Long startTime; // Timestamp
    
    @ColumnInfo(name = "end_time")
    public Long endTime; // Timestamp
    
    @ColumnInfo(name = "is_completed")
    public boolean isCompleted;
    
    @ColumnInfo(name = "priority")
    public int priority;
    
    @ColumnInfo(name = "category_id")
    public int categoryId;
    
    @ColumnInfo(name = "user_id")
    public int userId;
    
    @ColumnInfo(name = "created_at")
    public Long createdAt; // Timestamp
    
    @ColumnInfo(name = "updated_at")
    public Long updatedAt; // Timestamp
    
    // Constructor mặc định (required by Room)
    public TaskEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.isCompleted = false;
        this.priority = 1;
    }
    
    // Constructor đầy đủ
    public TaskEntity(String title, String description, Long startTime, Long endTime, 
                      int categoryId, int userId) {
        this();
        this.title = title;
        this.description = description;
        this.startTime = startTime;
        this.endTime = endTime;
        this.categoryId = categoryId;
        this.userId = userId;
    }
    
    // Getters và Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        this.updatedAt = System.currentTimeMillis();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = System.currentTimeMillis();
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
        this.updatedAt = System.currentTimeMillis();
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
        this.updatedAt = System.currentTimeMillis();
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        isCompleted = completed;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
        this.updatedAt = System.currentTimeMillis();
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Helper methods để convert Date
    public Date getStartTimeAsDate() {
        return startTime != null ? new Date(startTime) : null;
    }
    
    public void setStartTimeFromDate(Date date) {
        this.startTime = date != null ? date.getTime() : null;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public Date getEndTimeAsDate() {
        return endTime != null ? new Date(endTime) : null;
    }
    
    public void setEndTimeFromDate(Date date) {
        this.endTime = date != null ? date.getTime() : null;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public Date getCreatedAtAsDate() {
        return createdAt != null ? new Date(createdAt) : null;
    }
    
    public Date getUpdatedAtAsDate() {
        return updatedAt != null ? new Date(updatedAt) : null;
    }
}
