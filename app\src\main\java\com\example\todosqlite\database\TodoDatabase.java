package com.example.todosqlite.database;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.example.todosqlite.database.dao.CategoryDao;
import com.example.todosqlite.database.dao.TaskDao;
import com.example.todosqlite.database.dao.UserDao;
import com.example.todosqlite.database.entities.CategoryEntity;
import com.example.todosqlite.database.entities.TaskEntity;
import com.example.todosqlite.database.entities.UserEntity;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Room Database cho TodoApp
 */
@Database(
    entities = {UserEntity.class, CategoryEntity.class, TaskEntity.class},
    version = 1,
    exportSchema = false
)
public abstract class TodoDatabase extends RoomDatabase {
    
    // Abstract methods để lấy DAOs
    public abstract UserDao userDao();
    public abstract CategoryDao categoryDao();
    public abstract TaskDao taskDao();
    
    // Singleton instance
    private static volatile TodoDatabase INSTANCE;
    
    // Executor để chạy database operations trên background thread
    private static final int NUMBER_OF_THREADS = 4;
    public static final ExecutorService databaseWriteExecutor =
            Executors.newFixedThreadPool(NUMBER_OF_THREADS);
    
    /**
     * Lấy instance của database (Singleton pattern)
     */
    public static TodoDatabase getDatabase(final Context context) {
        if (INSTANCE == null) {
            synchronized (TodoDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            TodoDatabase.class,
                            "todo_database"
                    )
                    .addCallback(sRoomDatabaseCallback)
                    .build();
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * Callback để populate database với dữ liệu mẫu khi tạo lần đầu
     */
    private static RoomDatabase.Callback sRoomDatabaseCallback = new RoomDatabase.Callback() {
        @Override
        public void onCreate(@NonNull SupportSQLiteDatabase db) {
            super.onCreate(db);
            
            // Populate database với dữ liệu mẫu trên background thread
            databaseWriteExecutor.execute(() -> {
                populateInitialData(INSTANCE);
            });
        }
    };
    
    /**
     * Populate database với dữ liệu mẫu
     */
    private static void populateInitialData(TodoDatabase db) {
        UserDao userDao = db.userDao();
        CategoryDao categoryDao = db.categoryDao();
        TaskDao taskDao = db.taskDao();
        
        // Xóa tất cả dữ liệu cũ (nếu có)
        userDao.deleteAllUsers();
        categoryDao.deleteAllCategories();
        taskDao.deleteAllTasks();
        
        // Tạo user mặc định
        UserEntity defaultUser = new UserEntity("user1", "<EMAIL>", "Người dùng mặc định");
        long userId = userDao.insertUser(defaultUser);
        
        // Tạo categories mặc định
        CategoryEntity allCategory = new CategoryEntity("Tất cả", "Tất cả các nhiệm vụ", "#2196F3", "all_inclusive", (int) userId);
        CategoryEntity workCategory = new CategoryEntity("Công việc", "Các nhiệm vụ liên quan đến công việc", "#FF9800", "work", (int) userId);
        CategoryEntity personalCategory = new CategoryEntity("Cá nhân", "Các nhiệm vụ cá nhân", "#4CAF50", "person", (int) userId);
        
        long allCategoryId = categoryDao.insertCategory(allCategory);
        long workCategoryId = categoryDao.insertCategory(workCategory);
        long personalCategoryId = categoryDao.insertCategory(personalCategory);
        
        // Tạo một số tasks mẫu
        long currentTime = System.currentTimeMillis();
        long oneHourLater = currentTime + (60 * 60 * 1000); // 1 giờ sau
        long tomorrow = currentTime + (24 * 60 * 60 * 1000); // ngày mai
        
        TaskEntity task1 = new TaskEntity(
            "Hoàn thành báo cáo",
            "Viết báo cáo tổng kết tháng cho phòng ban",
            currentTime,
            oneHourLater,
            (int) workCategoryId,
            (int) userId
        );
        task1.setPriority(3); // High priority
        
        TaskEntity task2 = new TaskEntity(
            "Mua sắm cuối tuần",
            "Đi siêu thị mua thực phẩm cho tuần tới",
            tomorrow,
            tomorrow + (2 * 60 * 60 * 1000), // 2 giờ
            (int) personalCategoryId,
            (int) userId
        );
        task2.setPriority(1); // Low priority
        
        TaskEntity task3 = new TaskEntity(
            "Họp team",
            "Cuộc họp weekly với team development",
            currentTime + (2 * 60 * 60 * 1000), // 2 giờ sau
            currentTime + (3 * 60 * 60 * 1000), // 3 giờ sau
            (int) workCategoryId,
            (int) userId
        );
        task3.setPriority(2); // Medium priority
        
        taskDao.insertTask(task1);
        taskDao.insertTask(task2);
        taskDao.insertTask(task3);
        
        // Cập nhật task count cho categories
        categoryDao.updateTaskCount((int) allCategoryId, System.currentTimeMillis());
        categoryDao.updateTaskCount((int) workCategoryId, System.currentTimeMillis());
        categoryDao.updateTaskCount((int) personalCategoryId, System.currentTimeMillis());
    }
    
    /**
     * Đóng database
     */
    public static void closeDatabase() {
        if (INSTANCE != null) {
            INSTANCE.close();
            INSTANCE = null;
        }
    }
}
