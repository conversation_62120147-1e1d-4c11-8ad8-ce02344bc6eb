package com.example.todosqlite.models;

import java.util.Date;

/**
 * Model class cho Category (Danh mục)
 */
public class Category {
    private int id;
    private String name;            // Tên danh mục
    private String description;     // <PERSON><PERSON> tả danh mục
    private String color;           // <PERSON><PERSON><PERSON> sắc danh mục (hex code)
    private String icon;            // Icon danh mục
    private int userId;             // ID người dùng sở hữu
    private Date createdAt;         // Thời gian tạo
    private Date updatedAt;         // Thời gian cập nhật
    private int taskCount;          // Số lượng task trong danh mục

    // Constructor mặc định
    public Category() {
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.taskCount = 0;
        this.color = "#2196F3"; // Màu xanh mặc định
        this.icon = "folder";   // Icon mặc định
    }

    // Constructor với tên
    public Category(String name, int userId) {
        this();
        this.name = name;
        this.userId = userId;
    }

    // Constructor đầy đủ
    public Category(String name, String description, String color, String icon, int userId) {
        this();
        this.name = name;
        this.description = description;
        this.color = color;
        this.icon = icon;
        this.userId = userId;
    }

    // Getters và Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.updatedAt = new Date();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = new Date();
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
        this.updatedAt = new Date();
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
        this.updatedAt = new Date();
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
        this.updatedAt = new Date();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public int getTaskCount() {
        return taskCount;
    }

    public void setTaskCount(int taskCount) {
        this.taskCount = taskCount;
    }

    @Override
    public String toString() {
        return "Category{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", color='" + color + '\'' +
                ", taskCount=" + taskCount +
                '}';
    }
}
