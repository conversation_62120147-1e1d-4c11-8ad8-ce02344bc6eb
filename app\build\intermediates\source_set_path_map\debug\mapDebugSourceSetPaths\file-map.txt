com.example.todosqlite.app-lifecycle-livedata-2.7.0-0 C:\Users\<USER>\.gradle\caches\transforms-4\0d0b38d45d41b7c56819b271910a71d0\transformed\lifecycle-livedata-2.7.0\res
com.example.todosqlite.app-core-runtime-2.2.0-1 C:\Users\<USER>\.gradle\caches\transforms-4\0f84ae4d4214ebb27d7091d9ce3a3c00\transformed\core-runtime-2.2.0\res
com.example.todosqlite.app-navigation-common-2.7.6-2 C:\Users\<USER>\.gradle\caches\transforms-4\17c088bd53661f13ff0264fc857d2930\transformed\navigation-common-2.7.6\res
com.example.todosqlite.app-lifecycle-viewmodel-2.7.0-3 C:\Users\<USER>\.gradle\caches\transforms-4\18ca87bfd26d883815e2f1f4a134329a\transformed\lifecycle-viewmodel-2.7.0\res
com.example.todosqlite.app-jetified-emoji2-1.3.0-4 C:\Users\<USER>\.gradle\caches\transforms-4\195e99d5e6fc6ef2c8b247063a4fc2fd\transformed\jetified-emoji2-1.3.0\res
com.example.todosqlite.app-jetified-lifecycle-livedata-core-ktx-2.7.0-5 C:\Users\<USER>\.gradle\caches\transforms-4\1f35eff66c4aeb7d85acb3d7a1cdfb74\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.todosqlite.app-fragment-1.6.2-6 C:\Users\<USER>\.gradle\caches\transforms-4\1fddc67620e67c09c1c45dab59837547\transformed\fragment-1.6.2\res
com.example.todosqlite.app-cardview-1.0.0-7 C:\Users\<USER>\.gradle\caches\transforms-4\22d0783dd24d0d6ec5a3a756d362aae1\transformed\cardview-1.0.0\res
com.example.todosqlite.app-jetified-savedstate-ktx-1.2.1-8 C:\Users\<USER>\.gradle\caches\transforms-4\28e1fdd60913e51fcfc013a9b7ca419d\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.todosqlite.app-navigation-runtime-2.7.6-9 C:\Users\<USER>\.gradle\caches\transforms-4\31692a35a8350e01b068a97d8c631498\transformed\navigation-runtime-2.7.6\res
com.example.todosqlite.app-jetified-window-1.0.0-10 C:\Users\<USER>\.gradle\caches\transforms-4\33d7af5859d4b0ca542a42be2f1d09a8\transformed\jetified-window-1.0.0\res
com.example.todosqlite.app-jetified-activity-ktx-1.10.1-11 C:\Users\<USER>\.gradle\caches\transforms-4\4b997058f7ffc991c03f192a7da0e10f\transformed\jetified-activity-ktx-1.10.1\res
com.example.todosqlite.app-core-1.13.0-12 C:\Users\<USER>\.gradle\caches\transforms-4\55b720bce712d5f10fb40e251130c91a\transformed\core-1.13.0\res
com.example.todosqlite.app-sqlite-framework-2.4.0-13 C:\Users\<USER>\.gradle\caches\transforms-4\6270d9a27717adcf3c6fa8887813fed6\transformed\sqlite-framework-2.4.0\res
com.example.todosqlite.app-jetified-annotation-experimental-1.4.0-14 C:\Users\<USER>\.gradle\caches\transforms-4\63576dfc77e7fabf329187de59cbd3d5\transformed\jetified-annotation-experimental-1.4.0\res
com.example.todosqlite.app-coordinatorlayout-1.1.0-15 C:\Users\<USER>\.gradle\caches\transforms-4\724453f2ae173d8332d8b6c9f677ec25\transformed\coordinatorlayout-1.1.0\res
com.example.todosqlite.app-jetified-room-ktx-2.6.1-16 C:\Users\<USER>\.gradle\caches\transforms-4\741952f2986db0651787adb58ee6aac9\transformed\jetified-room-ktx-2.6.1\res
com.example.todosqlite.app-slidingpanelayout-1.2.0-17 C:\Users\<USER>\.gradle\caches\transforms-4\75d2c0e7e8f5aa8f4ca9cc51836d2bd6\transformed\slidingpanelayout-1.2.0\res
com.example.todosqlite.app-appcompat-1.7.1-18 C:\Users\<USER>\.gradle\caches\transforms-4\76a693482785115f724fb0ceaef9fe4b\transformed\appcompat-1.7.1\res
com.example.todosqlite.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-19 C:\Users\<USER>\.gradle\caches\transforms-4\772530bf60699e516c5d9c48116c9af0\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.todosqlite.app-lifecycle-livedata-core-2.7.0-20 C:\Users\<USER>\.gradle\caches\transforms-4\7b0330aadf1c0024a54d6dcb492bfdcd\transformed\lifecycle-livedata-core-2.7.0\res
com.example.todosqlite.app-sqlite-2.4.0-21 C:\Users\<USER>\.gradle\caches\transforms-4\7bcc07d6bba6bfee7c6080340d532a46\transformed\sqlite-2.4.0\res
com.example.todosqlite.app-jetified-fragment-ktx-1.6.2-22 C:\Users\<USER>\.gradle\caches\transforms-4\7d161b6912236283c47ed688d04b2b42\transformed\jetified-fragment-ktx-1.6.2\res
com.example.todosqlite.app-constraintlayout-2.2.1-23 C:\Users\<USER>\.gradle\caches\transforms-4\7d259899b47171ace4374beceb21bafb\transformed\constraintlayout-2.2.1\res
com.example.todosqlite.app-navigation-fragment-2.7.6-24 C:\Users\<USER>\.gradle\caches\transforms-4\7ee463c034f39916e679fc8f3280e100\transformed\navigation-fragment-2.7.6\res
com.example.todosqlite.app-drawerlayout-1.1.1-25 C:\Users\<USER>\.gradle\caches\transforms-4\89f69b07fc2f178d5bea8c04722e0b75\transformed\drawerlayout-1.1.1\res
com.example.todosqlite.app-jetified-appcompat-resources-1.7.1-26 C:\Users\<USER>\.gradle\caches\transforms-4\90c025dfcb3fb6c4154e6a1a22ceb938\transformed\jetified-appcompat-resources-1.7.1\res
com.example.todosqlite.app-lifecycle-runtime-2.7.0-27 C:\Users\<USER>\.gradle\caches\transforms-4\98c7bfcdceb1ec423e8f4c1b566e89c8\transformed\lifecycle-runtime-2.7.0\res
com.example.todosqlite.app-material-1.12.0-28 C:\Users\<USER>\.gradle\caches\transforms-4\9a29a62ff3fc1ea4fe1f5798df91a5f9\transformed\material-1.12.0\res
com.example.todosqlite.app-transition-1.5.0-29 C:\Users\<USER>\.gradle\caches\transforms-4\9ce69b2403fa5ae9d154a58d473d04bf\transformed\transition-1.5.0\res
com.example.todosqlite.app-room-runtime-2.6.1-30 C:\Users\<USER>\.gradle\caches\transforms-4\a251b7feeaca4f8967d31bbde036aa3a\transformed\room-runtime-2.6.1\res
com.example.todosqlite.app-jetified-core-viewtree-1.0.0-31 C:\Users\<USER>\.gradle\caches\transforms-4\ae360547d322bc7f67b0fdb627a89384\transformed\jetified-core-viewtree-1.0.0\res
com.example.todosqlite.app-jetified-emoji2-views-helper-1.3.0-32 C:\Users\<USER>\.gradle\caches\transforms-4\aee1dfa867bf21f23b83b7aee445f202\transformed\jetified-emoji2-views-helper-1.3.0\res
com.example.todosqlite.app-jetified-savedstate-1.2.1-33 C:\Users\<USER>\.gradle\caches\transforms-4\aeeb0b3e72fe0fe18365e7db50ee08aa\transformed\jetified-savedstate-1.2.1\res
com.example.todosqlite.app-jetified-lifecycle-process-2.7.0-34 C:\Users\<USER>\.gradle\caches\transforms-4\b3302bc95f3742ec5a56532917b0a0d6\transformed\jetified-lifecycle-process-2.7.0\res
com.example.todosqlite.app-recyclerview-1.3.2-35 C:\Users\<USER>\.gradle\caches\transforms-4\b8d06de8467e65510db13b02f7c59e54\transformed\recyclerview-1.3.2\res
com.example.todosqlite.app-jetified-profileinstaller-1.4.0-36 C:\Users\<USER>\.gradle\caches\transforms-4\cebd75928509a75ed50b26116314cc66\transformed\jetified-profileinstaller-1.4.0\res
com.example.todosqlite.app-jetified-customview-poolingcontainer-1.0.0-37 C:\Users\<USER>\.gradle\caches\transforms-4\d116f9ebe95b2d4350adca82f74dc9d7\transformed\jetified-customview-poolingcontainer-1.0.0\res
com.example.todosqlite.app-navigation-ui-2.7.6-38 C:\Users\<USER>\.gradle\caches\transforms-4\db0f45af4afa27c4564c6b21d1e85aea\transformed\navigation-ui-2.7.6\res
com.example.todosqlite.app-jetified-startup-runtime-1.1.1-39 C:\Users\<USER>\.gradle\caches\transforms-4\dc2059fd8da65232a83fd07a022cfd39\transformed\jetified-startup-runtime-1.1.1\res
com.example.todosqlite.app-jetified-lifecycle-runtime-ktx-2.7.0-40 C:\Users\<USER>\.gradle\caches\transforms-4\e5ffb640da85ff215d46a38fef2a0e8b\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.todosqlite.app-jetified-core-ktx-1.13.0-41 C:\Users\<USER>\.gradle\caches\transforms-4\eade1f9cbf2965ce6bcc6abb79b104c7\transformed\jetified-core-ktx-1.13.0\res
com.example.todosqlite.app-jetified-lifecycle-viewmodel-ktx-2.7.0-42 C:\Users\<USER>\.gradle\caches\transforms-4\ee70f50c38823baad7cde3cd8ede0bc4\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.todosqlite.app-jetified-viewpager2-1.1.0-beta02-43 C:\Users\<USER>\.gradle\caches\transforms-4\f529a41b7826e1bb21c2773cfb4ee9a8\transformed\jetified-viewpager2-1.1.0-beta02\res
com.example.todosqlite.app-jetified-activity-1.10.1-44 C:\Users\<USER>\.gradle\caches\transforms-4\f7dbad2754aa67eae6f9c1cdb8ff2d9b\transformed\jetified-activity-1.10.1\res
com.example.todosqlite.app-pngs-45 D:\TodoSqlite\app\build\generated\res\pngs\debug
com.example.todosqlite.app-resValues-46 D:\TodoSqlite\app\build\generated\res\resValues\debug
com.example.todosqlite.app-packageDebugResources-47 D:\TodoSqlite\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.todosqlite.app-packageDebugResources-48 D:\TodoSqlite\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.todosqlite.app-debug-49 D:\TodoSqlite\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.todosqlite.app-debug-50 D:\TodoSqlite\app\src\debug\res
com.example.todosqlite.app-main-51 D:\TodoSqlite\app\src\main\res
