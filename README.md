# TodoSqlite - Ứng dụng quản lý nhiệm vụ

Ứng dụng TodoList đ<PERSON><PERSON><PERSON> xây dựng bằng Android với SQLite database, có giao diện hiện đại và đầy đủ tính năng.

## 🌟 Tính năng chính

### 📱 Giao diện chính
- **Hamburger Menu**: <PERSON><PERSON><PERSON> cập nhanh các chức năng
- **Thanh tìm kiếm**: Tìm kiếm nhiệm vụ theo tiêu đề và mô tả
- **Danh sách danh mục**: Hiển thị các danh mục với số lượng nhiệm vụ
- **Danh sách nhiệm vụ**: Hiển thị nhiệm vụ với thông tin chi tiết
- **Bottom Navigation**: Chuyển đổi giữa Tasks, Calendar, Profile

### 📋 Quản lý nhiệm vụ
- **Thêm nhiệm vụ mới**: <PERSON><PERSON><PERSON> ti<PERSON> đ<PERSON>, m<PERSON>, th<PERSON><PERSON>ian, đ<PERSON> <PERSON>u tiên
- **Chỉnh sửa nhiệm vụ**: <PERSON><PERSON><PERSON> nhật thông tin nhiệm vụ
- **Đánh dấu hoàn thành**: Checkbox để đánh dấu nhiệm vụ đã xong
- **Xóa nhiệm vụ**: Xóa nhiệm vụ không cần thiết
- **Độ ưu tiên**: 3 mức độ ưu tiên (Thấp, Trung bình, Cao)

### 🗂️ Quản lý danh mục
- **Thêm danh mục**: Tạo danh mục mới với tên và màu sắc
- **Chọn màu sắc**: 8 màu sắc định sẵn cho danh mục
- **Đếm nhiệm vụ**: Hiển thị số lượng nhiệm vụ trong mỗi danh mục
- **Lọc theo danh mục**: Xem nhiệm vụ theo danh mục cụ thể

### 📅 Lịch
- **Calendar View**: Xem nhiệm vụ theo ngày
- **Chọn ngày**: Nhấn vào ngày để xem nhiệm vụ
- **Hiển thị nhiệm vụ**: Danh sách nhiệm vụ cho ngày được chọn

### 👤 Hồ sơ cá nhân
- **Thông tin người dùng**: Hiển thị tên và email
- **Thống kê**: Tổng số nhiệm vụ, đã hoàn thành, đang thực hiện
- **Cài đặt**: Truy cập các tùy chọn cài đặt

## 🏗️ Cấu trúc dự án

```
app/src/main/java/com/example/todosqlite/
├── activities/
│   ├── AddTaskActivity.java      # Thêm nhiệm vụ mới
│   └── AddCategoryActivity.java  # Thêm danh mục mới
├── adapters/
│   ├── TaskAdapter.java          # Adapter cho danh sách nhiệm vụ
│   └── CategoryAdapter.java      # Adapter cho danh sách danh mục
├── database/
│   ├── DatabaseHelper.java       # SQLite database helper
│   ├── TaskDAO.java              # Data Access Object cho Task
│   ├── CategoryDAO.java          # Data Access Object cho Category
│   └── UserDAO.java              # Data Access Object cho User
├── fragments/
│   ├── TaskFragment.java         # Fragment hiển thị nhiệm vụ
│   ├── CalendarFragment.java     # Fragment hiển thị lịch
│   └── ProfileFragment.java      # Fragment hồ sơ cá nhân
├── models/
│   ├── Task.java                 # Model cho nhiệm vụ
│   ├── Category.java             # Model cho danh mục
│   └── User.java                 # Model cho người dùng
├── utils/
│   ├── Constants.java            # Các hằng số
│   └── DateUtils.java            # Tiện ích xử lý ngày tháng
└── MainActivity.java             # Activity chính
```

## 🗄️ Database Schema

### Bảng Users
- `id` (INTEGER PRIMARY KEY)
- `username` (TEXT UNIQUE)
- `email` (TEXT UNIQUE)
- `full_name` (TEXT)
- `avatar` (TEXT)
- `phone` (TEXT)
- `date_of_birth` (INTEGER)
- `gender` (TEXT)
- `last_login_at` (INTEGER)
- `is_active` (INTEGER)
- `created_at` (INTEGER)
- `updated_at` (INTEGER)

### Bảng Categories
- `id` (INTEGER PRIMARY KEY)
- `name` (TEXT)
- `description` (TEXT)
- `color` (TEXT)
- `icon` (TEXT)
- `user_id` (INTEGER FOREIGN KEY)
- `task_count` (INTEGER)
- `created_at` (INTEGER)
- `updated_at` (INTEGER)

### Bảng Tasks
- `id` (INTEGER PRIMARY KEY)
- `title` (TEXT)
- `description` (TEXT)
- `start_time` (INTEGER)
- `end_time` (INTEGER)
- `is_completed` (INTEGER)
- `priority` (INTEGER)
- `category_id` (INTEGER FOREIGN KEY)
- `user_id` (INTEGER FOREIGN KEY)
- `created_at` (INTEGER)
- `updated_at` (INTEGER)

## 🚀 Cách chạy ứng dụng

1. **Clone repository**:
   ```bash
   git clone <repository-url>
   cd TodoSqlite
   ```

2. **Mở trong Android Studio**:
   - Mở Android Studio
   - Chọn "Open an existing project"
   - Chọn thư mục TodoSqlite

3. **Build và chạy**:
   - Đợi Gradle sync hoàn thành
   - Nhấn "Run" hoặc Shift+F10
   - Chọn device/emulator để chạy

## 📱 Yêu cầu hệ thống

- **Android SDK**: API level 28 trở lên (Android 9.0+)
- **Target SDK**: API level 34
- **Java**: Version 8
- **Gradle**: 8.6

## 🎨 Thư viện sử dụng

- **AndroidX AppCompat**: Tương thích ngược
- **Material Design Components**: Giao diện Material Design
- **RecyclerView**: Hiển thị danh sách
- **Navigation Component**: Điều hướng giữa các Fragment
- **CardView**: Hiển thị thẻ
- **ConstraintLayout**: Layout linh hoạt

## 📝 Ghi chú

- Ứng dụng sử dụng SQLite để lưu trữ dữ liệu local
- Dữ liệu mẫu được tạo tự động khi khởi chạy lần đầu
- Giao diện được thiết kế theo Material Design 3
- Hỗ trợ tiếng Việt

## 🔮 Tính năng tương lai

- [ ] Thông báo nhắc nhở
- [ ] Đồng bộ cloud
- [ ] Chia sẻ nhiệm vụ
- [ ] Dark mode
- [ ] Export/Import dữ liệu
- [ ] Widget màn hình chính

## 👨‍💻 Tác giả

Được phát triển bởi Augment Agent với yêu cầu từ người dùng.
