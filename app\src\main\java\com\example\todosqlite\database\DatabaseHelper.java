package com.example.todosqlite.database;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

/**
 * Database Helper class để quản lý SQLite database
 */
public class DatabaseHelper extends SQLiteOpenHelper {
    
    // Database info
    private static final String DATABASE_NAME = "TodoApp.db";
    private static final int DATABASE_VERSION = 1;
    
    // Table names
    public static final String TABLE_USERS = "users";
    public static final String TABLE_CATEGORIES = "categories";
    public static final String TABLE_TASKS = "tasks";
    
    // Common column names
    public static final String COLUMN_ID = "id";
    public static final String COLUMN_CREATED_AT = "created_at";
    public static final String COLUMN_UPDATED_AT = "updated_at";
    
    // Users table columns
    public static final String COLUMN_USERNAME = "username";
    public static final String COLUMN_EMAIL = "email";
    public static final String COLUMN_FULL_NAME = "full_name";
    public static final String COLUMN_AVATAR = "avatar";
    public static final String COLUMN_PHONE = "phone";
    public static final String COLUMN_DATE_OF_BIRTH = "date_of_birth";
    public static final String COLUMN_GENDER = "gender";
    public static final String COLUMN_LAST_LOGIN_AT = "last_login_at";
    public static final String COLUMN_IS_ACTIVE = "is_active";
    
    // Categories table columns
    public static final String COLUMN_NAME = "name";
    public static final String COLUMN_DESCRIPTION = "description";
    public static final String COLUMN_COLOR = "color";
    public static final String COLUMN_ICON = "icon";
    public static final String COLUMN_USER_ID = "user_id";
    public static final String COLUMN_TASK_COUNT = "task_count";
    
    // Tasks table columns
    public static final String COLUMN_TITLE = "title";
    public static final String COLUMN_START_TIME = "start_time";
    public static final String COLUMN_END_TIME = "end_time";
    public static final String COLUMN_IS_COMPLETED = "is_completed";
    public static final String COLUMN_PRIORITY = "priority";
    public static final String COLUMN_CATEGORY_ID = "category_id";
    
    // Create table statements
    private static final String CREATE_TABLE_USERS = 
        "CREATE TABLE " + TABLE_USERS + " (" +
        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_USERNAME + " TEXT UNIQUE NOT NULL, " +
        COLUMN_EMAIL + " TEXT UNIQUE NOT NULL, " +
        COLUMN_FULL_NAME + " TEXT NOT NULL, " +
        COLUMN_AVATAR + " TEXT, " +
        COLUMN_PHONE + " TEXT, " +
        COLUMN_DATE_OF_BIRTH + " INTEGER, " +
        COLUMN_GENDER + " TEXT, " +
        COLUMN_LAST_LOGIN_AT + " INTEGER, " +
        COLUMN_IS_ACTIVE + " INTEGER DEFAULT 1, " +
        COLUMN_CREATED_AT + " INTEGER NOT NULL, " +
        COLUMN_UPDATED_AT + " INTEGER NOT NULL" +
        ")";
    
    private static final String CREATE_TABLE_CATEGORIES = 
        "CREATE TABLE " + TABLE_CATEGORIES + " (" +
        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_NAME + " TEXT NOT NULL, " +
        COLUMN_DESCRIPTION + " TEXT, " +
        COLUMN_COLOR + " TEXT DEFAULT '#2196F3', " +
        COLUMN_ICON + " TEXT DEFAULT 'folder', " +
        COLUMN_USER_ID + " INTEGER NOT NULL, " +
        COLUMN_TASK_COUNT + " INTEGER DEFAULT 0, " +
        COLUMN_CREATED_AT + " INTEGER NOT NULL, " +
        COLUMN_UPDATED_AT + " INTEGER NOT NULL, " +
        "FOREIGN KEY(" + COLUMN_USER_ID + ") REFERENCES " + TABLE_USERS + "(" + COLUMN_ID + ")" +
        ")";
    
    private static final String CREATE_TABLE_TASKS = 
        "CREATE TABLE " + TABLE_TASKS + " (" +
        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_TITLE + " TEXT NOT NULL, " +
        COLUMN_DESCRIPTION + " TEXT, " +
        COLUMN_START_TIME + " INTEGER, " +
        COLUMN_END_TIME + " INTEGER, " +
        COLUMN_IS_COMPLETED + " INTEGER DEFAULT 0, " +
        COLUMN_PRIORITY + " INTEGER DEFAULT 1, " +
        COLUMN_CATEGORY_ID + " INTEGER NOT NULL, " +
        COLUMN_USER_ID + " INTEGER NOT NULL, " +
        COLUMN_CREATED_AT + " INTEGER NOT NULL, " +
        COLUMN_UPDATED_AT + " INTEGER NOT NULL, " +
        "FOREIGN KEY(" + COLUMN_CATEGORY_ID + ") REFERENCES " + TABLE_CATEGORIES + "(" + COLUMN_ID + "), " +
        "FOREIGN KEY(" + COLUMN_USER_ID + ") REFERENCES " + TABLE_USERS + "(" + COLUMN_ID + ")" +
        ")";
    
    private static DatabaseHelper instance;
    
    public static synchronized DatabaseHelper getInstance(Context context) {
        if (instance == null) {
            instance = new DatabaseHelper(context.getApplicationContext());
        }
        return instance;
    }
    
    private DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(CREATE_TABLE_USERS);
        db.execSQL(CREATE_TABLE_CATEGORIES);
        db.execSQL(CREATE_TABLE_TASKS);
        
        // Insert default data
        insertDefaultData(db);
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_TASKS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_CATEGORIES);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_USERS);
        onCreate(db);
    }
    
    @Override
    public void onConfigure(SQLiteDatabase db) {
        super.onConfigure(db);
        db.setForeignKeyConstraintsEnabled(true);
    }
    
    private void insertDefaultData(SQLiteDatabase db) {
        long currentTime = System.currentTimeMillis();
        
        // Insert default user
        String insertUser = "INSERT INTO " + TABLE_USERS + " (" +
            COLUMN_USERNAME + ", " + COLUMN_EMAIL + ", " + COLUMN_FULL_NAME + ", " +
            COLUMN_CREATED_AT + ", " + COLUMN_UPDATED_AT + ") VALUES " +
            "('user1', '<EMAIL>', 'Người dùng mặc định', " + currentTime + ", " + currentTime + ")";
        db.execSQL(insertUser);
        
        // Insert default categories
        String insertCategories = "INSERT INTO " + TABLE_CATEGORIES + " (" +
            COLUMN_NAME + ", " + COLUMN_DESCRIPTION + ", " + COLUMN_COLOR + ", " + COLUMN_ICON + ", " +
            COLUMN_USER_ID + ", " + COLUMN_CREATED_AT + ", " + COLUMN_UPDATED_AT + ") VALUES " +
            "('Tất cả', 'Tất cả các nhiệm vụ', '#2196F3', 'all_inclusive', 1, " + currentTime + ", " + currentTime + "), " +
            "('Công việc', 'Các nhiệm vụ liên quan đến công việc', '#FF9800', 'work', 1, " + currentTime + ", " + currentTime + "), " +
            "('Cá nhân', 'Các nhiệm vụ cá nhân', '#4CAF50', 'person', 1, " + currentTime + ", " + currentTime + ")";
        db.execSQL(insertCategories);
    }
}
