package com.example.todosqlite.database;

import android.content.Context;

import com.example.todosqlite.database.entities.SubTaskEntity;
import com.example.todosqlite.models.SubTask;
import com.example.todosqlite.utils.EntityConverter;

import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object cho SubTask - Room wrapper để maintain compatibility
 */
public class SubTaskDAO {
    private TodoDatabase database;
    
    public SubTaskDAO(Context context) {
        database = TodoDatabase.getDatabase(context);
    }
    
    // Thêm subtask mới
    public long insertSubTask(SubTask subTask) {
        try {
            SubTaskEntity entity = EntityConverter.toSubTaskEntity(subTask);
            return database.subTaskDao().insertSubTask(entity);
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }
    
    // Cập nhật subtask
    public int updateSubTask(SubTask subTask) {
        try {
            SubTaskEntity entity = EntityConverter.toSubTaskEntity(subTask);
            database.subTaskDao().updateSubTask(entity);
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    // Xóa subtask
    public int deleteSubTask(int subTaskId) {
        try {
            database.subTaskDao().deleteSubTaskById(subTaskId);
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    // Lấy subtask theo ID
    public SubTask getSubTaskById(int subTaskId) {
        try {
            SubTaskEntity entity = database.subTaskDao().getSubTaskById(subTaskId);
            return EntityConverter.toSubTask(entity);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    // Lấy tất cả subtask của một task
    public List<SubTask> getSubTasksByParentTask(int parentTaskId) {
        try {
            List<SubTaskEntity> entities = database.subTaskDao().getSubTasksByParentTask(parentTaskId);
            return EntityConverter.toSubTaskList(entities);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    // Lấy subtask đã hoàn thành
    public List<SubTask> getCompletedSubTasks(int parentTaskId) {
        try {
            List<SubTaskEntity> entities = database.subTaskDao().getCompletedSubTasks(parentTaskId);
            return EntityConverter.toSubTaskList(entities);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    // Lấy subtask chưa hoàn thành
    public List<SubTask> getPendingSubTasks(int parentTaskId) {
        try {
            List<SubTaskEntity> entities = database.subTaskDao().getPendingSubTasks(parentTaskId);
            return EntityConverter.toSubTaskList(entities);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    // Đếm số subtask
    public int getSubTaskCount(int parentTaskId) {
        try {
            return database.subTaskDao().getSubTaskCount(parentTaskId);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    
    // Đếm số subtask đã hoàn thành
    public int getCompletedSubTaskCount(int parentTaskId) {
        try {
            return database.subTaskDao().getCompletedSubTaskCount(parentTaskId);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
