package com.example.todosqlite.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CalendarView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.todosqlite.R;
import com.example.todosqlite.adapters.TaskAdapter;
import com.example.todosqlite.database.TaskDAO;
import com.example.todosqlite.models.Task;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Fragment hiển thị lịch và nhiệm vụ theo ngày
 */
public class CalendarFragment extends Fragment {
    
    private CalendarView calendarView;
    private TextView tvSelectedDate, tvTaskCountForDay;
    private RecyclerView rvDayTasks;
    private TaskAdapter taskAdapter;
    private TaskDAO taskDAO;
    private List<Task> dayTasks;
    private Date selectedDate;
    
    public CalendarFragment() {
        // Required empty public constructor
    }
    
    public static CalendarFragment newInstance() {
        return new CalendarFragment();
    }
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        taskDAO = new TaskDAO(getContext());
        dayTasks = new ArrayList<>();
        selectedDate = new Date(); // Today
    }
    
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_calendar, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupCalendar();
        setupRecyclerView();
        loadTasksForDate(selectedDate);
    }
    
    private void initViews(View view) {
        calendarView = view.findViewById(R.id.calendar_view);
        tvSelectedDate = view.findViewById(R.id.tv_selected_date);
        tvTaskCountForDay = view.findViewById(R.id.tv_task_count_for_day);
        rvDayTasks = view.findViewById(R.id.rv_day_tasks);
    }
    
    private void setupCalendar() {
        // Set today as selected date
        calendarView.setDate(selectedDate.getTime());
        updateSelectedDateText();
        
        calendarView.setOnDateChangeListener(new CalendarView.OnDateChangeListener() {
            @Override
            public void onSelectedDayChange(@NonNull CalendarView view, int year, int month, int dayOfMonth) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(year, month, dayOfMonth);
                selectedDate = calendar.getTime();
                
                updateSelectedDateText();
                loadTasksForDate(selectedDate);
            }
        });
    }
    
    private void setupRecyclerView() {
        taskAdapter = new TaskAdapter(getContext(), dayTasks);
        rvDayTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        rvDayTasks.setAdapter(taskAdapter);
        
        taskAdapter.setOnTaskClickListener(new TaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskClick(Task task) {
                // Handle task click
            }
            
            @Override
            public void onTaskCompleteToggle(Task task) {
                task.setCompleted(!task.isCompleted());
                taskDAO.updateTask(task);
                taskAdapter.notifyDataSetChanged();
            }
            
            @Override
            public void onTaskOptionsClick(Task task) {
                // Handle task options
            }
        });
    }
    
    private void updateSelectedDateText() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEEE, dd MMMM yyyy", new Locale("vi", "VN"));
        tvSelectedDate.setText(dateFormat.format(selectedDate));
    }
    
    private void loadTasksForDate(Date date) {
        dayTasks.clear();
        
        // Get all tasks for user
        List<Task> allTasks = taskDAO.getAllTasksByUser(1);
        
        // Filter tasks for selected date
        Calendar selectedCal = Calendar.getInstance();
        selectedCal.setTime(date);
        
        for (Task task : allTasks) {
            if (task.getStartTime() != null) {
                Calendar taskCal = Calendar.getInstance();
                taskCal.setTime(task.getStartTime());
                
                if (selectedCal.get(Calendar.YEAR) == taskCal.get(Calendar.YEAR) &&
                    selectedCal.get(Calendar.DAY_OF_YEAR) == taskCal.get(Calendar.DAY_OF_YEAR)) {
                    dayTasks.add(task);
                }
            }
        }

        // Update task count display
        tvTaskCountForDay.setText(dayTasks.size() + " nhiệm vụ");

        taskAdapter.notifyDataSetChanged();
    }
    
    public void refreshTasks() {
        loadTasksForDate(selectedDate);
    }
}
