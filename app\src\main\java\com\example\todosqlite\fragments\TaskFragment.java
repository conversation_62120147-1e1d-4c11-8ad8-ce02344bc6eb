package com.example.todosqlite.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.todosqlite.R;
import com.example.todosqlite.adapters.TaskAdapter;
import com.example.todosqlite.database.TaskDAO;
import com.example.todosqlite.models.Task;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment hiển thị danh sách nhiệm vụ
 */
public class TaskFragment extends Fragment {
    
    private RecyclerView recyclerView;
    private TaskAdapter taskAdapter;
    private TaskDAO taskDAO;
    private List<Task> taskList;
    
    public TaskFragment() {
        // Required empty public constructor
    }
    
    public static TaskFragment newInstance() {
        return new TaskFragment();
    }
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        taskDAO = new TaskDAO(getContext());
        taskList = new ArrayList<>();
    }
    
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_task, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupRecyclerView();
        loadTasks();
    }
    
    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.rv_tasks);
    }
    
    private void setupRecyclerView() {
        taskAdapter = new TaskAdapter(getContext(), taskList);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.setAdapter(taskAdapter);
        
        // Set item click listener
        taskAdapter.setOnTaskClickListener(new TaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskClick(Task task) {
                // Handle task click
                // TODO: Open task detail or edit activity
            }
            
            @Override
            public void onTaskCompleteToggle(Task task) {
                // Handle task completion toggle
                task.setCompleted(!task.isCompleted());
                taskDAO.updateTask(task);
                taskAdapter.notifyDataSetChanged();
            }
            
            @Override
            public void onTaskOptionsClick(Task task) {
                // Handle task options click
                // TODO: Show options menu (edit, delete, etc.)
            }
        });
    }
    
    private void loadTasks() {
        // Load tasks for default user (ID = 1)
        taskList.clear();
        taskList.addAll(taskDAO.getAllTasksByUser(1));
        taskAdapter.notifyDataSetChanged();
    }
    
    public void refreshTasks() {
        loadTasks();
    }
    
    public void filterTasksByCategory(int categoryId) {
        taskList.clear();
        if (categoryId == -1) {
            // Show all tasks
            taskList.addAll(taskDAO.getAllTasksByUser(1));
        } else {
            // Show tasks by category
            taskList.addAll(taskDAO.getTasksByCategory(categoryId));
        }
        taskAdapter.notifyDataSetChanged();
    }
    
    public void searchTasks(String query) {
        taskList.clear();
        if (query.isEmpty()) {
            taskList.addAll(taskDAO.getAllTasksByUser(1));
        } else {
            taskList.addAll(taskDAO.searchTasks(1, query));
        }
        taskAdapter.notifyDataSetChanged();
    }
}
