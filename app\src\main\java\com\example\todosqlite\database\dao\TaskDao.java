package com.example.todosqlite.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.todosqlite.database.entities.TaskEntity;

import java.util.List;

/**
 * Room DAO cho Task
 */
@Dao
public interface TaskDao {
    
    @Insert
    long insertTask(TaskEntity task);
    
    @Update
    void updateTask(TaskEntity task);
    
    @Delete
    void deleteTask(TaskEntity task);
    
    @Query("SELECT * FROM tasks WHERE id = :taskId")
    TaskEntity getTaskById(int taskId);
    
    @Query("SELECT * FROM tasks WHERE id = :taskId")
    LiveData<TaskEntity> getTaskByIdLive(int taskId);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId ORDER BY created_at DESC")
    List<TaskEntity> getAllTasksByUser(int userId);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> getAllTasksByUserLive(int userId);
    
    @Query("SELECT * FROM tasks WHERE category_id = :categoryId ORDER BY created_at DESC")
    List<TaskEntity> getTasksByCategory(int categoryId);
    
    @Query("SELECT * FROM tasks WHERE category_id = :categoryId ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> getTasksByCategoryLive(int categoryId);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND is_completed = 1 ORDER BY updated_at DESC")
    List<TaskEntity> getCompletedTasks(int userId);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND is_completed = 1 ORDER BY updated_at DESC")
    LiveData<List<TaskEntity>> getCompletedTasksLive(int userId);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND is_completed = 0 ORDER BY created_at DESC")
    List<TaskEntity> getPendingTasks(int userId);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND is_completed = 0 ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> getPendingTasksLive(int userId);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND " +
           "(title LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%') " +
           "ORDER BY created_at DESC")
    List<TaskEntity> searchTasks(int userId, String query);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND " +
           "(title LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%') " +
           "ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> searchTasksLive(int userId, String query);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND priority = :priority ORDER BY created_at DESC")
    List<TaskEntity> getTasksByPriority(int userId, int priority);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND priority = :priority ORDER BY created_at DESC")
    LiveData<List<TaskEntity>> getTasksByPriorityLive(int userId, int priority);
    
    // Query để lấy tasks trong khoảng thời gian
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND " +
           "start_time >= :startTime AND start_time <= :endTime " +
           "ORDER BY start_time ASC")
    List<TaskEntity> getTasksInTimeRange(int userId, long startTime, long endTime);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND " +
           "start_time >= :startTime AND start_time <= :endTime " +
           "ORDER BY start_time ASC")
    LiveData<List<TaskEntity>> getTasksInTimeRangeLive(int userId, long startTime, long endTime);
    
    // Query để lấy tasks hôm nay
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND " +
           "DATE(start_time/1000, 'unixepoch') = DATE(:todayTimestamp/1000, 'unixepoch') " +
           "ORDER BY start_time ASC")
    List<TaskEntity> getTasksForToday(int userId, long todayTimestamp);
    
    @Query("SELECT * FROM tasks WHERE user_id = :userId AND " +
           "DATE(start_time/1000, 'unixepoch') = DATE(:todayTimestamp/1000, 'unixepoch') " +
           "ORDER BY start_time ASC")
    LiveData<List<TaskEntity>> getTasksForTodayLive(int userId, long todayTimestamp);
    
    // Thống kê
    @Query("SELECT COUNT(*) FROM tasks WHERE user_id = :userId")
    int getTotalTaskCount(int userId);
    
    @Query("SELECT COUNT(*) FROM tasks WHERE user_id = :userId AND is_completed = 1")
    int getCompletedTaskCount(int userId);
    
    @Query("SELECT COUNT(*) FROM tasks WHERE user_id = :userId AND is_completed = 0")
    int getPendingTaskCount(int userId);
    
    @Query("SELECT COUNT(*) FROM tasks WHERE category_id = :categoryId")
    int getTaskCountByCategory(int categoryId);
    
    // Xóa
    @Query("DELETE FROM tasks WHERE id = :taskId")
    void deleteTaskById(int taskId);
    
    @Query("DELETE FROM tasks WHERE category_id = :categoryId")
    void deleteTasksByCategory(int categoryId);
    
    @Query("DELETE FROM tasks WHERE user_id = :userId")
    void deleteAllTasksByUser(int userId);
    
    @Query("DELETE FROM tasks")
    void deleteAllTasks();
    
    // Cập nhật trạng thái hoàn thành
    @Query("UPDATE tasks SET is_completed = :isCompleted, updated_at = :timestamp WHERE id = :taskId")
    void updateTaskCompletion(int taskId, boolean isCompleted, long timestamp);
}
