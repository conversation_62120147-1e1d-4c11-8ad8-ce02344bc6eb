package com.example.todosqlite.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.todosqlite.database.entities.SubTaskEntity;

import java.util.List;

/**
 * Room DAO cho SubTask
 */
@Dao
public interface SubTaskDao {
    
    @Insert
    long insertSubTask(SubTaskEntity subTask);
    
    @Update
    void updateSubTask(SubTaskEntity subTask);
    
    @Delete
    void deleteSubTask(SubTaskEntity subTask);
    
    @Query("SELECT * FROM subtasks WHERE id = :subTaskId")
    SubTaskEntity getSubTaskById(int subTaskId);
    
    @Query("SELECT * FROM subtasks WHERE id = :subTaskId")
    LiveData<SubTaskEntity> getSubTaskByIdLive(int subTaskId);
    
    @Query("SELECT * FROM subtasks WHERE parent_task_id = :parentTaskId ORDER BY order_index ASC, created_at ASC")
    List<SubTaskEntity> getSubTasksByParentTask(int parentTaskId);
    
    @Query("SELECT * FROM subtasks WHERE parent_task_id = :parentTaskId ORDER BY order_index ASC, created_at ASC")
    LiveData<List<SubTaskEntity>> getSubTasksByParentTaskLive(int parentTaskId);
    
    @Query("SELECT * FROM subtasks WHERE parent_task_id = :parentTaskId AND is_completed = 1 ORDER BY updated_at DESC")
    List<SubTaskEntity> getCompletedSubTasks(int parentTaskId);
    
    @Query("SELECT * FROM subtasks WHERE parent_task_id = :parentTaskId AND is_completed = 0 ORDER BY order_index ASC, created_at ASC")
    List<SubTaskEntity> getPendingSubTasks(int parentTaskId);
    
    @Query("SELECT COUNT(*) FROM subtasks WHERE parent_task_id = :parentTaskId")
    int getSubTaskCount(int parentTaskId);
    
    @Query("SELECT COUNT(*) FROM subtasks WHERE parent_task_id = :parentTaskId AND is_completed = 1")
    int getCompletedSubTaskCount(int parentTaskId);
    
    @Query("SELECT COUNT(*) FROM subtasks WHERE parent_task_id = :parentTaskId AND is_completed = 0")
    int getPendingSubTaskCount(int parentTaskId);
    
    @Query("UPDATE subtasks SET is_completed = :isCompleted, updated_at = :timestamp WHERE id = :subTaskId")
    void updateSubTaskCompletion(int subTaskId, boolean isCompleted, long timestamp);
    
    @Query("UPDATE subtasks SET order_index = :order, updated_at = :timestamp WHERE id = :subTaskId")
    void updateSubTaskOrder(int subTaskId, int order, long timestamp);
    
    @Query("DELETE FROM subtasks WHERE id = :subTaskId")
    void deleteSubTaskById(int subTaskId);
    
    @Query("DELETE FROM subtasks WHERE parent_task_id = :parentTaskId")
    void deleteSubTasksByParentTask(int parentTaskId);
    
    @Query("DELETE FROM subtasks")
    void deleteAllSubTasks();
}
