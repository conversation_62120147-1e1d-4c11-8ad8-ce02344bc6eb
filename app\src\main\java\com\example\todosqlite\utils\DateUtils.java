package com.example.todosqlite.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * Utility class để xử lý ngày tháng
 */
public class DateUtils {
    
    private static final Locale VIETNAMESE_LOCALE = new Locale("vi", "VN");
    
    /**
     * Format date theo định dạng dd/MM/yyyy
     */
    public static String formatDate(Date date) {
        if (date == null) return "";
        SimpleDateFormat format = new SimpleDateFormat(Constants.DATE_FORMAT, VIETNAMESE_LOCALE);
        return format.format(date);
    }
    
    /**
     * Format time theo định dạng HH:mm
     */
    public static String formatTime(Date date) {
        if (date == null) return "";
        SimpleDateFormat format = new SimpleDateFormat(Constants.TIME_FORMAT, VIETNAMESE_LOCALE);
        return format.format(date);
    }
    
    /**
     * Format datetime theo định dạng dd/MM/yyyy HH:mm
     */
    public static String formatDateTime(Date date) {
        if (date == null) return "";
        SimpleDateFormat format = new SimpleDateFormat(Constants.DATETIME_FORMAT, VIETNAMESE_LOCALE);
        return format.format(date);
    }
    
    /**
     * Format date để hiển thị (Thứ hai, 01 tháng 1 năm 2024)
     */
    public static String formatDisplayDate(Date date) {
        if (date == null) return "";
        SimpleDateFormat format = new SimpleDateFormat(Constants.DISPLAY_DATE_FORMAT, VIETNAMESE_LOCALE);
        return format.format(date);
    }
    
    /**
     * Kiểm tra xem hai ngày có cùng ngày không
     */
    public static boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) return false;
        
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }
    
    /**
     * Kiểm tra xem ngày có phải hôm nay không
     */
    public static boolean isToday(Date date) {
        return isSameDay(date, new Date());
    }
    
    /**
     * Kiểm tra xem ngày có phải ngày mai không
     */
    public static boolean isTomorrow(Date date) {
        Calendar tomorrow = Calendar.getInstance();
        tomorrow.add(Calendar.DAY_OF_MONTH, 1);
        return isSameDay(date, tomorrow.getTime());
    }
    
    /**
     * Kiểm tra xem ngày có phải hôm qua không
     */
    public static boolean isYesterday(Date date) {
        Calendar yesterday = Calendar.getInstance();
        yesterday.add(Calendar.DAY_OF_MONTH, -1);
        return isSameDay(date, yesterday.getTime());
    }
    
    /**
     * Lấy ngày bắt đầu của ngày (00:00:00)
     */
    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
    
    /**
     * Lấy ngày kết thúc của ngày (23:59:59)
     */
    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
    
    /**
     * Tạo text hiển thị thời gian tương đối (hôm nay, ngày mai, etc.)
     */
    public static String getRelativeTimeText(Date date) {
        if (date == null) return "";
        
        if (isToday(date)) {
            return "Hôm nay, " + formatTime(date);
        } else if (isTomorrow(date)) {
            return "Ngày mai, " + formatTime(date);
        } else if (isYesterday(date)) {
            return "Hôm qua, " + formatTime(date);
        } else {
            return formatDateTime(date);
        }
    }
    
    /**
     * Tính số ngày giữa hai ngày
     */
    public static int daysBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) return 0;
        
        long diffInMillies = Math.abs(endDate.getTime() - startDate.getTime());
        return (int) (diffInMillies / (1000 * 60 * 60 * 24));
    }
}
