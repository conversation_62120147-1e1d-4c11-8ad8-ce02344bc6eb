#Tue Jul 01 15:38:06 ICT 2025
com.example.todosqlite.app-main-47\:/mipmap-anydpi/ic_launcher.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.example.todosqlite.app-main-47\:/mipmap-hdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-47\:/menu/drawer_menu.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_drawer_menu.xml.flat
com.example.todosqlite.app-main-47\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-47\:/drawable/ic_launcher_foreground.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_launcher_background.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.todosqlite.app-main-47\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-47\:/drawable/priority_background.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_priority_background.xml.flat
com.example.todosqlite.app-main-47\:/mipmap-mdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-47\:/xml/data_extraction_rules.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_task.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_task.xml.flat
com.example.todosqlite.app-main-47\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-47\:/drawable/ic_check.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check.xml.flat
com.example.todosqlite.app-main-47\:/layout/fragment_task.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_task.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-47\:/layout/item_category.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-mdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-47\:/drawable/ic_work.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_work.xml.flat
com.example.todosqlite.app-main-34\:/drawable/ic_launcher_foreground.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-47\:/layout/nav_header_main.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_nav_header_main.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-47\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-47\:/drawable/ic_settings.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_add.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-anydpi/ic_launcher.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.example.todosqlite.app-main-47\:/layout/activity_main.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_pending.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pending.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_empty_tasks.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_empty_tasks.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-hdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-47\:/drawable/circle_background.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background.xml.flat
com.example.todosqlite.app-main-47\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-34\:/xml/backup_rules.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_search.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search.xml.flat
com.example.todosqlite.app-main-47\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-47\:/layout/item_task.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-47\:/drawable/ic_info.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_info.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_more_vert.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_more_vert.xml.flat
com.example.todosqlite.app-main-47\:/xml/backup_rules.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_person.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_person.xml.flat
com.example.todosqlite.app-main-47\:/mipmap-xhdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-34\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-34\:/layout/activity_main.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.todosqlite.app-main-34\:/drawable/ic_launcher_background.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.todosqlite.app-main-34\:/xml/data_extraction_rules.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-47\:/drawable/nav_header_bg.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_nav_header_bg.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-34\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_time.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_time.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_menu.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.xml.flat
com.example.todosqlite.app-main-34\:/mipmap-xhdpi/ic_launcher.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.todosqlite.app-main-47\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.example.todosqlite.app-main-47\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.todosqlite.app-main-47\:/menu/bottom_navigation_menu.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_navigation_menu.xml.flat
com.example.todosqlite.app-main-47\:/drawable/ic_calendar.xml=D\:\\TodoSqlite\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calendar.xml.flat
