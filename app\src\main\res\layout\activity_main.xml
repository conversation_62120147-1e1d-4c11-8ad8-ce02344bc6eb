<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- Main content -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- App Bar -->
        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <!-- Menu Button -->
                    <ImageButton
                        android:id="@+id/btn_menu"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="Menu"
                        android:src="@drawable/ic_menu"
                        android:layout_marginEnd="8dp" />

                    <!-- Search View -->
                    <androidx.appcompat.widget.SearchView
                        android:id="@+id/search_view"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="16dp"
                        app:iconifiedByDefault="false"
                        app:queryHint="Tìm kiếm nhiệm vụ..."
                        app:searchIcon="@drawable/ic_search" />

                </LinearLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <!-- Main Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <!-- Category List -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_categories"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    tools:listitem="@layout/item_category" />

                <!-- Add Category Button -->
                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:id="@+id/fab_add_category"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/ic_add"
                    app:fabSize="mini"
                    android:contentDescription="Thêm danh mục" />

            </LinearLayout>

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color" />

            <!-- Task List Container -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <!-- Task List -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_tasks"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="16dp"
                    android:clipToPadding="false"
                    tools:listitem="@layout/item_task" />

                <!-- Add Task Button -->
                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:id="@+id/fab_add_task"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|end"
                    android:layout_margin="16dp"
                    android:src="@drawable/ic_add"
                    android:contentDescription="Thêm nhiệm vụ" />

            </FrameLayout>

        </LinearLayout>

        <!-- Bottom Navigation -->
        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            app:menu="@menu/bottom_navigation_menu"
            app:labelVisibilityMode="labeled" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:headerLayout="@layout/nav_header_main"
        app:menu="@menu/drawer_menu" />

</androidx.drawerlayout.widget.DrawerLayout>