<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Category Name -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Tên danh mục"
                app:boxStrokeColor="?attr/colorPrimary"
                app:hintTextColor="?attr/colorPrimary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_category_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Category Description -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="Mô tả danh mục (tùy chọn)"
                app:boxStrokeColor="?attr/colorPrimary"
                app:hintTextColor="?attr/colorPrimary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_category_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:lines="2"
                    android:gravity="top" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Color Selection -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Chọn màu sắc"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="12dp" />

            <!-- Selected Color Preview -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Màu đã chọn:"
                    android:textSize="14sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginEnd="12dp" />

                <View
                    android:id="@+id/view_selected_color"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/circle_background"
                    android:elevation="2dp" />

            </LinearLayout>

            <!-- Color Picker Grid -->
            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="4"
                android:rowCount="2"
                android:layout_marginBottom="24dp">

                <View
                    android:id="@+id/color_1"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_margin="8dp"
                    android:background="@drawable/color_circle"
                    android:clickable="true"
                    android:focusable="true"
                    android:elevation="2dp" />

                <View
                    android:id="@+id/color_2"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_margin="8dp"
                    android:background="@drawable/color_circle"
                    android:clickable="true"
                    android:focusable="true"
                    android:elevation="2dp" />

                <View
                    android:id="@+id/color_3"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_margin="8dp"
                    android:background="@drawable/color_circle"
                    android:clickable="true"
                    android:focusable="true"
                    android:elevation="2dp" />

                <View
                    android:id="@+id/color_4"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_margin="8dp"
                    android:background="@drawable/color_circle"
                    android:clickable="true"
                    android:focusable="true"
                    android:elevation="2dp" />

                <View
                    android:id="@+id/color_5"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_margin="8dp"
                    android:background="@drawable/color_circle"
                    android:clickable="true"
                    android:focusable="true"
                    android:elevation="2dp" />

                <View
                    android:id="@+id/color_6"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_margin="8dp"
                    android:background="@drawable/color_circle"
                    android:clickable="true"
                    android:focusable="true"
                    android:elevation="2dp" />

                <View
                    android:id="@+id/color_7"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_margin="8dp"
                    android:background="@drawable/color_circle"
                    android:clickable="true"
                    android:focusable="true"
                    android:elevation="2dp" />

                <View
                    android:id="@+id/color_8"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_margin="8dp"
                    android:background="@drawable/color_circle"
                    android:clickable="true"
                    android:focusable="true"
                    android:elevation="2dp" />

            </GridLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/surface_color"
        android:elevation="4dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Hủy"
            android:textColor="?attr/colorOnSurface"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Lưu"
            android:textColor="@android:color/white"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

</LinearLayout>
