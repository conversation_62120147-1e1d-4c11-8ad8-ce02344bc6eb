package com.example.todosqlite.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.todosqlite.database.entities.CategoryEntity;

import java.util.List;

/**
 * Room DAO cho Category
 */
@Dao
public interface CategoryDao {
    
    @Insert
    long insertCategory(CategoryEntity category);
    
    @Update
    void updateCategory(CategoryEntity category);
    
    @Delete
    void deleteCategory(CategoryEntity category);
    
    @Query("SELECT * FROM categories WHERE id = :categoryId")
    CategoryEntity getCategoryById(int categoryId);
    
    @Query("SELECT * FROM categories WHERE id = :categoryId")
    LiveData<CategoryEntity> getCategoryByIdLive(int categoryId);
    
    @Query("SELECT * FROM categories WHERE user_id = :userId ORDER BY created_at ASC")
    List<CategoryEntity> getAllCategoriesByUser(int userId);
    
    @Query("SELECT * FROM categories WHERE user_id = :userId ORDER BY created_at ASC")
    LiveData<List<CategoryEntity>> getAllCategoriesByUserLive(int userId);
    
    @Query("SELECT c.*, COUNT(t.id) as task_count " +
           "FROM categories c " +
           "LEFT JOIN tasks t ON c.id = t.category_id " +
           "WHERE c.user_id = :userId " +
           "GROUP BY c.id " +
           "ORDER BY c.created_at ASC")
    List<CategoryEntity> getCategoriesWithTaskCount(int userId);
    
    @Query("SELECT c.*, COUNT(t.id) as task_count " +
           "FROM categories c " +
           "LEFT JOIN tasks t ON c.id = t.category_id " +
           "WHERE c.user_id = :userId " +
           "GROUP BY c.id " +
           "ORDER BY c.created_at ASC")
    LiveData<List<CategoryEntity>> getCategoriesWithTaskCountLive(int userId);
    
    @Query("UPDATE categories SET task_count = (" +
           "SELECT COUNT(*) FROM tasks WHERE category_id = :categoryId" +
           "), updated_at = :timestamp WHERE id = :categoryId")
    void updateTaskCount(int categoryId, long timestamp);
    
    @Query("SELECT * FROM categories WHERE name = :name AND user_id = :userId LIMIT 1")
    CategoryEntity getCategoryByName(String name, int userId);
    
    @Query("SELECT COUNT(*) FROM categories WHERE user_id = :userId")
    int getCategoryCountByUser(int userId);
    
    @Query("DELETE FROM categories WHERE id = :categoryId")
    void deleteCategoryById(int categoryId);
    
    @Query("DELETE FROM categories WHERE user_id = :userId")
    void deleteAllCategoriesByUser(int userId);
    
    @Query("DELETE FROM categories")
    void deleteAllCategories();
    
    // Query để lấy category phổ biến nhất (có nhiều task nhất)
    @Query("SELECT c.* FROM categories c " +
           "LEFT JOIN tasks t ON c.id = t.category_id " +
           "WHERE c.user_id = :userId " +
           "GROUP BY c.id " +
           "ORDER BY COUNT(t.id) DESC " +
           "LIMIT 1")
    CategoryEntity getMostUsedCategory(int userId);
    
    // Query để lấy categories theo màu sắc
    @Query("SELECT * FROM categories WHERE color = :color AND user_id = :userId")
    List<CategoryEntity> getCategoriesByColor(String color, int userId);
}
