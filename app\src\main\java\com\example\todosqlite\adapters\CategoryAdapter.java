package com.example.todosqlite.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.card.MaterialCardView;

import com.example.todosqlite.R;
import com.example.todosqlite.models.Category;

import java.util.List;

/**
 * Adapter cho RecyclerView hiển thị danh sách Category
 */
public class CategoryAdapter extends RecyclerView.Adapter<CategoryAdapter.CategoryViewHolder> {
    
    private Context context;
    private List<Category> categoryList;
    private OnCategoryClickListener listener;
    private int selectedCategoryId = -1; // -1 means all categories
    
    public interface OnCategoryClickListener {
        void onCategoryClick(Category category);
    }
    
    public CategoryAdapter(Context context, List<Category> categoryList) {
        this.context = context;
        this.categoryList = categoryList;
    }
    
    public void setOnCategoryClickListener(OnCategoryClickListener listener) {
        this.listener = listener;
    }
    
    public void setSelectedCategory(int categoryId) {
        this.selectedCategoryId = categoryId;
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_category, parent, false);
        return new CategoryViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        Category category = categoryList.get(position);
        holder.bind(category);
    }
    
    @Override
    public int getItemCount() {
        return categoryList.size();
    }
    
    class CategoryViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private View viewCategoryColor;
        private TextView tvCategoryName, tvTaskCount;
        
        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);

            cardView = (MaterialCardView) itemView;
            viewCategoryColor = itemView.findViewById(R.id.view_category_color);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
            tvTaskCount = itemView.findViewById(R.id.tv_task_count);
        }
        
        public void bind(Category category) {
            // Set category name
            tvCategoryName.setText(category.getName());
            
            // Set task count with better formatting
            int taskCount = category.getTaskCount();
            if (taskCount == 0) {
                tvTaskCount.setText("Trống");
            } else if (taskCount == 1) {
                tvTaskCount.setText("1 task");
            } else {
                tvTaskCount.setText(taskCount + " tasks");
            }
            
            // Set category color
            setCategoryColor(category.getColor());
            
            // Set selection state
            boolean isSelected = (selectedCategoryId == category.getId());
            updateSelectionState(isSelected);
            
            // Set click listener
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onCategoryClick(category);
                }
            });
        }
        
        private void setCategoryColor(String colorHex) {
            try {
                if (colorHex != null && !colorHex.isEmpty()) {
                    int color = Color.parseColor(colorHex);
                    // Create a colored circle drawable
                    android.graphics.drawable.GradientDrawable drawable =
                        (android.graphics.drawable.GradientDrawable) context.getDrawable(R.drawable.category_color_circle).mutate();
                    drawable.setColor(color);
                    viewCategoryColor.setBackground(drawable);
                } else {
                    // Use default color
                    android.graphics.drawable.GradientDrawable drawable =
                        (android.graphics.drawable.GradientDrawable) context.getDrawable(R.drawable.category_color_circle).mutate();
                    drawable.setColor(context.getColor(R.color.primary_color));
                    viewCategoryColor.setBackground(drawable);
                }
            } catch (IllegalArgumentException e) {
                // Use default color if parsing fails
                android.graphics.drawable.GradientDrawable drawable =
                    (android.graphics.drawable.GradientDrawable) context.getDrawable(R.drawable.category_color_circle).mutate();
                drawable.setColor(context.getColor(R.color.primary_color));
                viewCategoryColor.setBackground(drawable);
            }
        }
        
        private void updateSelectionState(boolean isSelected) {
            if (isSelected) {
                // Selected state: thicker border, higher elevation, full opacity
                cardView.setStrokeWidth(4);
                cardView.setStrokeColor(context.getColor(R.color.primary_color));
                cardView.setCardElevation(6f);
                cardView.setAlpha(1.0f);
                cardView.setScaleX(1.05f);
                cardView.setScaleY(1.05f);
            } else {
                // Normal state: thin border, normal elevation, slightly transparent
                cardView.setStrokeWidth(1);
                cardView.setStrokeColor(context.getColor(R.color.card_stroke_color));
                cardView.setCardElevation(3f);
                cardView.setAlpha(0.9f);
                cardView.setScaleX(1.0f);
                cardView.setScaleY(1.0f);
            }
        }
    }
}
