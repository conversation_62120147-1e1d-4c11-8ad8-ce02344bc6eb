package com.example.todosqlite.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.card.MaterialCardView;

import com.example.todosqlite.R;
import com.example.todosqlite.models.Category;

import java.util.List;

/**
 * Adapter cho RecyclerView hiển thị danh sách Category
 */
public class CategoryAdapter extends RecyclerView.Adapter<CategoryAdapter.CategoryViewHolder> {
    
    private Context context;
    private List<Category> categoryList;
    private OnCategoryClickListener listener;
    private int selectedCategoryId = -1; // -1 means all categories
    
    public interface OnCategoryClickListener {
        void onCategoryClick(Category category);
    }
    
    public CategoryAdapter(Context context, List<Category> categoryList) {
        this.context = context;
        this.categoryList = categoryList;
    }
    
    public void setOnCategoryClickListener(OnCategoryClickListener listener) {
        this.listener = listener;
    }
    
    public void setSelectedCategory(int categoryId) {
        this.selectedCategoryId = categoryId;
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_category, parent, false);
        return new CategoryViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        Category category = categoryList.get(position);
        holder.bind(category);
    }
    
    @Override
    public int getItemCount() {
        return categoryList.size();
    }
    
    class CategoryViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private View viewCategoryColor;
        private TextView tvCategoryName, tvTaskCount;
        
        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);

            cardView = (MaterialCardView) itemView;
            viewCategoryColor = itemView.findViewById(R.id.view_category_color);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
            tvTaskCount = itemView.findViewById(R.id.tv_task_count);
        }
        
        public void bind(Category category) {
            // Set category name
            tvCategoryName.setText(category.getName());
            
            // Set task count
            tvTaskCount.setText(String.valueOf(category.getTaskCount()));
            
            // Set category color
            setCategoryColor(category.getColor());
            
            // Set selection state
            boolean isSelected = (selectedCategoryId == category.getId());
            updateSelectionState(isSelected);
            
            // Set click listener
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onCategoryClick(category);
                }
            });
        }
        
        private void setCategoryColor(String colorHex) {
            try {
                if (colorHex != null && !colorHex.isEmpty()) {
                    int color = Color.parseColor(colorHex);
                    viewCategoryColor.setBackgroundColor(color);
                } else {
                    // Use default color
                    viewCategoryColor.setBackgroundColor(context.getColor(R.color.primary_color));
                }
            } catch (IllegalArgumentException e) {
                // Use default color if parsing fails
                viewCategoryColor.setBackgroundColor(context.getColor(R.color.primary_color));
            }
        }
        
        private void updateSelectionState(boolean isSelected) {
            if (isSelected) {
                cardView.setStrokeWidth(6);
                cardView.setStrokeColor(context.getColor(R.color.primary_color));
                cardView.setCardElevation(8f);
                cardView.setAlpha(1.0f);
            } else {
                cardView.setStrokeWidth(2);
                cardView.setStrokeColor(context.getColor(R.color.card_stroke_color));
                cardView.setCardElevation(3f);
                cardView.setAlpha(0.8f);
            }
        }
    }
}
