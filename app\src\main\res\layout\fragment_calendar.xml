<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color"
    tools:context=".fragments.CalendarFragment">

    <!-- Calendar View -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="3dp">

        <CalendarView
            android:id="@+id/calendar_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="8dp" />

    </com.google.android.material.card.MaterialCardView>

    <!-- Selected Date Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_selected_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Hôm nay"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface" />

        <TextView
            android:id="@+id/tv_task_count_for_day"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0 nhiệm vụ"
            android:textSize="14sp"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:background="@drawable/rounded_background"
            android:paddingHorizontal="12dp"
            android:paddingVertical="4dp" />

    </LinearLayout>

    <!-- Tasks for Selected Date -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_day_tasks"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp"
        android:clipToPadding="false"
        tools:listitem="@layout/item_task" />

    <!-- Empty state for no tasks -->
    <LinearLayout
        android:id="@+id/layout_empty_day"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:src="@drawable/ic_calendar_empty"
            android:alpha="0.5"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Không có nhiệm vụ nào"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hãy thêm nhiệm vụ cho ngày này"
            android:textSize="14sp"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:gravity="center" />

    </LinearLayout>

</LinearLayout>
