package com.example.todosqlite.utils;

/**
 * Class chứa các hằng số của ứng dụng
 */
public class Constants {
    
    // Default User ID
    public static final int DEFAULT_USER_ID = 1;
    
    // Priority levels
    public static final int PRIORITY_LOW = 1;
    public static final int PRIORITY_MEDIUM = 2;
    public static final int PRIORITY_HIGH = 3;
    
    // Category IDs for special categories
    public static final int CATEGORY_ALL = -1;
    
    // Date formats
    public static final String DATE_FORMAT = "dd/MM/yyyy";
    public static final String TIME_FORMAT = "HH:mm";
    public static final String DATETIME_FORMAT = "dd/MM/yyyy HH:mm";
    public static final String DISPLAY_DATE_FORMAT = "EEEE, dd MMMM yyyy";
    
    // Default colors
    public static final String DEFAULT_CATEGORY_COLOR = "#2196F3";
    public static final String[] CATEGORY_COLORS = {
        "#2196F3", "#4CAF50", "#FF9800", "#F44336", 
        "#9C27B0", "#607D8B", "#795548", "#E91E63"
    };
    
    // Intent extras
    public static final String EXTRA_TASK_ID = "task_id";
    public static final String EXTRA_CATEGORY_ID = "category_id";
    public static final String EXTRA_SELECTED_DATE = "selected_date";
    
    // Shared preferences keys
    public static final String PREF_NAME = "TodoAppPrefs";
    public static final String PREF_FIRST_RUN = "first_run";
    public static final String PREF_CURRENT_USER_ID = "current_user_id";
    public static final String PREF_NOTIFICATION_ENABLED = "notification_enabled";
    
    // Request codes
    public static final int REQUEST_ADD_TASK = 1001;
    public static final int REQUEST_EDIT_TASK = 1002;
    public static final int REQUEST_ADD_CATEGORY = 1003;
    public static final int REQUEST_EDIT_CATEGORY = 1004;
    
    // Database
    public static final String DATABASE_NAME = "TodoApp.db";
    public static final int DATABASE_VERSION = 1;
}
