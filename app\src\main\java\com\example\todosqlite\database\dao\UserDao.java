package com.example.todosqlite.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.todosqlite.database.entities.UserEntity;

import java.util.List;

/**
 * Room DAO cho User
 */
@Dao
public interface UserDao {
    
    @Insert
    long insertUser(UserEntity user);
    
    @Update
    void updateUser(UserEntity user);
    
    @Delete
    void deleteUser(UserEntity user);
    
    @Query("SELECT * FROM users WHERE id = :userId")
    UserEntity getUserById(int userId);
    
    @Query("SELECT * FROM users WHERE id = :userId")
    LiveData<UserEntity> getUserByIdLive(int userId);
    
    @Query("SELECT * FROM users WHERE username = :username LIMIT 1")
    UserEntity getUserByUsername(String username);
    
    @Query("SELECT * FROM users WHERE email = :email LIMIT 1")
    UserEntity getUserByEmail(String email);
    
    @Query("SELECT * FROM users ORDER BY created_at DESC")
    List<UserEntity> getAllUsers();
    
    @Query("SELECT * FROM users ORDER BY created_at DESC")
    LiveData<List<UserEntity>> getAllUsersLive();
    
    @Query("SELECT * FROM users WHERE is_active = 1 ORDER BY last_login_at DESC")
    List<UserEntity> getActiveUsers();
    
    @Query("SELECT * FROM users WHERE is_active = 1 ORDER BY last_login_at DESC")
    LiveData<List<UserEntity>> getActiveUsersLive();
    
    @Query("UPDATE users SET last_login_at = :timestamp, updated_at = :timestamp WHERE id = :userId")
    void updateLastLogin(int userId, long timestamp);
    
    @Query("UPDATE users SET is_active = :isActive, updated_at = :timestamp WHERE id = :userId")
    void updateActiveStatus(int userId, boolean isActive, long timestamp);
    
    @Query("SELECT COUNT(*) FROM users")
    int getUserCount();
    
    @Query("SELECT COUNT(*) FROM users WHERE is_active = 1")
    int getActiveUserCount();
    
    @Query("DELETE FROM users WHERE id = :userId")
    void deleteUserById(int userId);
    
    @Query("DELETE FROM users")
    void deleteAllUsers();
}
