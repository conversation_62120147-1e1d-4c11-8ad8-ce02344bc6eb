<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_color">#FF4081</color>
    <color name="background_color">#FAFAFA</color>
    <color name="black">#FF000000</color>
    <color name="card_stroke_color">#E0E0E0</color>
    <color name="category_all">#2196F3</color>
    <color name="category_personal">#4CAF50</color>
    <color name="category_work">#FF9800</color>
    <color name="divider_color">#E0E0E0</color>
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark_color">#1976D2</color>
    <color name="priority_high">#F44336</color>
    <color name="priority_low">#4CAF50</color>
    <color name="priority_medium">#FF9800</color>
    <color name="surface_color">#FFFFFF</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">3dp</dimen>
    <dimen name="card_margin">8dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <dimen name="text_size_tiny">12sp</dimen>
    <string name="app_name">TodoSqlite</string>
    <style name="Base.Theme.TodoSqlite" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="Theme.TodoSqlite" parent="Base.Theme.TodoSqlite"/>
</resources>