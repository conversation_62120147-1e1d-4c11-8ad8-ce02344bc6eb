package com.example.todosqlite.adapters;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.todosqlite.R;
import com.example.todosqlite.database.CategoryDAO;
import com.example.todosqlite.models.Category;
import com.example.todosqlite.models.Task;
import com.example.todosqlite.utils.DateUtils;

import java.util.List;

/**
 * Adapter cho RecyclerView hiển thị danh sách Task
 */
public class TaskAdapter extends RecyclerView.Adapter<TaskAdapter.TaskViewHolder> {
    
    private Context context;
    private List<Task> taskList;
    private OnTaskClickListener listener;
    private CategoryDAO categoryDAO;
    
    public interface OnTaskClickListener {
        void onTaskClick(Task task);
        void onTaskCompleteToggle(Task task);
        void onTaskOptionsClick(Task task);
    }
    
    public TaskAdapter(Context context, List<Task> taskList) {
        this.context = context;
        this.taskList = taskList;
        this.categoryDAO = new CategoryDAO(context);
    }
    
    public void setOnTaskClickListener(OnTaskClickListener listener) {
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public TaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_task, parent, false);
        return new TaskViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull TaskViewHolder holder, int position) {
        Task task = taskList.get(position);
        holder.bind(task);
    }
    
    @Override
    public int getItemCount() {
        return taskList.size();
    }
    
    class TaskViewHolder extends RecyclerView.ViewHolder {
        private CheckBox cbCompleted;
        private TextView tvTitle, tvDescription, tvTime, tvPriority;
        private View viewCategoryIndicator;
        private ImageButton btnOptions;
        
        public TaskViewHolder(@NonNull View itemView) {
            super(itemView);
            
            cbCompleted = itemView.findViewById(R.id.cb_task_completed);
            tvTitle = itemView.findViewById(R.id.tv_task_title);
            tvDescription = itemView.findViewById(R.id.tv_task_description);
            tvTime = itemView.findViewById(R.id.tv_task_time);
            tvPriority = itemView.findViewById(R.id.tv_priority);
            viewCategoryIndicator = itemView.findViewById(R.id.view_category_indicator);
            btnOptions = itemView.findViewById(R.id.btn_task_options);
        }
        
        public void bind(Task task) {
            // Set task title
            tvTitle.setText(task.getTitle());
            
            // Set task description
            if (task.getDescription() != null && !task.getDescription().isEmpty()) {
                tvDescription.setText(task.getDescription());
                tvDescription.setVisibility(View.VISIBLE);
            } else {
                tvDescription.setVisibility(View.GONE);
            }
            
            // Set completion status
            cbCompleted.setChecked(task.isCompleted());
            updateTaskAppearance(task.isCompleted());
            
            // Set time
            if (task.getStartTime() != null) {
                tvTime.setText(DateUtils.getRelativeTimeText(task.getStartTime()));
                tvTime.setVisibility(View.VISIBLE);
            } else {
                tvTime.setVisibility(View.GONE);
            }
            
            // Set priority
            setPriority(task.getPriority());
            
            // Set category indicator color
            setCategoryIndicator(task.getCategoryId());
            
            // Set click listeners
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onTaskClick(task);
                }
            });
            
            cbCompleted.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onTaskCompleteToggle(task);
                }
            });
            
            btnOptions.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onTaskOptionsClick(task);
                }
            });
        }
        
        private void updateTaskAppearance(boolean isCompleted) {
            if (isCompleted) {
                tvTitle.setPaintFlags(tvTitle.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                tvTitle.setAlpha(0.6f);
                tvDescription.setAlpha(0.6f);
                tvTime.setAlpha(0.6f);
            } else {
                tvTitle.setPaintFlags(tvTitle.getPaintFlags() & (~Paint.STRIKE_THRU_TEXT_FLAG));
                tvTitle.setAlpha(1.0f);
                tvDescription.setAlpha(1.0f);
                tvTime.setAlpha(1.0f);
            }
        }
        
        private void setPriority(int priority) {
            String priorityText;
            int priorityColor;
            
            switch (priority) {
                case 1:
                    priorityText = context.getString(R.string.priority_low);
                    priorityColor = context.getColor(R.color.priority_low);
                    break;
                case 2:
                    priorityText = context.getString(R.string.priority_medium);
                    priorityColor = context.getColor(R.color.priority_medium);
                    break;
                case 3:
                default:
                    priorityText = context.getString(R.string.priority_high);
                    priorityColor = context.getColor(R.color.priority_high);
                    break;
            }
            
            tvPriority.setText(priorityText);
            tvPriority.setBackgroundColor(priorityColor);
            
            // Hide priority if it's low priority
            if (priority == 1) {
                tvPriority.setVisibility(View.GONE);
            } else {
                tvPriority.setVisibility(View.VISIBLE);
            }
        }
        
        private void setCategoryIndicator(int categoryId) {
            Category category = categoryDAO.getCategoryById(categoryId);
            if (category != null && category.getColor() != null) {
                try {
                    int color = Color.parseColor(category.getColor());
                    viewCategoryIndicator.setBackgroundColor(color);
                } catch (IllegalArgumentException e) {
                    // Use default color if parsing fails
                    viewCategoryIndicator.setBackgroundColor(context.getColor(R.color.primary_color));
                }
            } else {
                viewCategoryIndicator.setBackgroundColor(context.getColor(R.color.primary_color));
            }
        }
    }
}
