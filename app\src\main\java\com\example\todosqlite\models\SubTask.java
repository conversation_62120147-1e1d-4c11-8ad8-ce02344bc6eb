package com.example.todosqlite.models;

import java.util.Date;

/**
 * Model class cho SubTask (nhiệm vụ con)
 */
public class SubTask {
    private int id;
    private String title;
    private String description;
    private boolean isCompleted;
    private int parentTaskId;
    private int order; // Thứ tự hiển thị
    private Date createdAt;
    private Date updatedAt;
    
    // Constructor mặc định
    public SubTask() {
        this.isCompleted = false;
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.order = 0;
    }
    
    // Constructor với title
    public SubTask(String title, int parentTaskId) {
        this();
        this.title = title;
        this.parentTaskId = parentTaskId;
    }
    
    // Constructor đầy đủ
    public SubTask(String title, String description, int parentTaskId) {
        this();
        this.title = title;
        this.description = description;
        this.parentTaskId = parentTaskId;
    }
    
    // Getters và Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        this.updatedAt = new Date();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = new Date();
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        isCompleted = completed;
        this.updatedAt = new Date();
    }

    public int getParentTaskId() {
        return parentTaskId;
    }

    public void setParentTaskId(int parentTaskId) {
        this.parentTaskId = parentTaskId;
        this.updatedAt = new Date();
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
        this.updatedAt = new Date();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "SubTask{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", isCompleted=" + isCompleted +
                ", parentTaskId=" + parentTaskId +
                '}';
    }
}
