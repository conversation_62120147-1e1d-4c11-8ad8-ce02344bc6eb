package com.example.todosqlite.utils;

import com.example.todosqlite.database.entities.CategoryEntity;
import com.example.todosqlite.database.entities.SubTaskEntity;
import com.example.todosqlite.database.entities.TaskEntity;
import com.example.todosqlite.database.entities.UserEntity;
import com.example.todosqlite.models.Category;
import com.example.todosqlite.models.SubTask;
import com.example.todosqlite.models.Task;
import com.example.todosqlite.models.User;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class để convert giữa Entity và Model objects
 */
public class EntityConverter {
    
    // ==================== USER CONVERSIONS ====================
    
    /**
     * Convert UserEntity thành User model
     */
    public static User toUser(UserEntity entity) {
        if (entity == null) return null;
        
        User user = new User();
        user.setId(entity.getId());
        user.setUsername(entity.getUsername());
        user.setEmail(entity.getEmail());
        user.setFullName(entity.getFullName());
        user.setAvatar(entity.getAvatar());
        user.setPhone(entity.getPhone());
        user.setDateOfBirth(entity.getDateOfBirthAsDate());
        user.setGender(entity.getGender());
        user.setLastLoginAt(entity.getLastLoginAsDate());
        user.setActive(entity.isActive());
        user.setCreatedAt(entity.getCreatedAtAsDate());
        user.setUpdatedAt(entity.getUpdatedAtAsDate());
        
        return user;
    }
    
    /**
     * Convert User model thành UserEntity
     */
    public static UserEntity toUserEntity(User user) {
        if (user == null) return null;
        
        UserEntity entity = new UserEntity();
        entity.setId(user.getId());
        entity.setUsername(user.getUsername());
        entity.setEmail(user.getEmail());
        entity.setFullName(user.getFullName());
        entity.setAvatar(user.getAvatar());
        entity.setPhone(user.getPhone());
        entity.setDateOfBirthFromDate(user.getDateOfBirth());
        entity.setGender(user.getGender());
        entity.setLastLoginFromDate(user.getLastLoginAt());
        entity.setActive(user.isActive());
        entity.setCreatedAt(user.getCreatedAt() != null ? user.getCreatedAt().getTime() : null);
        entity.setUpdatedAt(user.getUpdatedAt() != null ? user.getUpdatedAt().getTime() : null);
        
        return entity;
    }
    
    /**
     * Convert List<UserEntity> thành List<User>
     */
    public static List<User> toUserList(List<UserEntity> entities) {
        if (entities == null) return null;
        
        List<User> users = new ArrayList<>();
        for (UserEntity entity : entities) {
            users.add(toUser(entity));
        }
        return users;
    }
    
    // ==================== CATEGORY CONVERSIONS ====================
    
    /**
     * Convert CategoryEntity thành Category model
     */
    public static Category toCategory(CategoryEntity entity) {
        if (entity == null) return null;
        
        Category category = new Category();
        category.setId(entity.getId());
        category.setName(entity.getName());
        category.setDescription(entity.getDescription());
        category.setColor(entity.getColor());
        category.setIcon(entity.getIcon());
        category.setUserId(entity.getUserId());
        category.setTaskCount(entity.getTaskCount());
        category.setCreatedAt(entity.getCreatedAtAsDate());
        category.setUpdatedAt(entity.getUpdatedAtAsDate());
        
        return category;
    }
    
    /**
     * Convert Category model thành CategoryEntity
     */
    public static CategoryEntity toCategoryEntity(Category category) {
        if (category == null) return null;
        
        CategoryEntity entity = new CategoryEntity();
        entity.setId(category.getId());
        entity.setName(category.getName());
        entity.setDescription(category.getDescription());
        entity.setColor(category.getColor());
        entity.setIcon(category.getIcon());
        entity.setUserId(category.getUserId());
        entity.setTaskCount(category.getTaskCount());
        entity.setCreatedAt(category.getCreatedAt() != null ? category.getCreatedAt().getTime() : null);
        entity.setUpdatedAt(category.getUpdatedAt() != null ? category.getUpdatedAt().getTime() : null);
        
        return entity;
    }
    
    /**
     * Convert List<CategoryEntity> thành List<Category>
     */
    public static List<Category> toCategoryList(List<CategoryEntity> entities) {
        if (entities == null) return null;
        
        List<Category> categories = new ArrayList<>();
        for (CategoryEntity entity : entities) {
            categories.add(toCategory(entity));
        }
        return categories;
    }
    
    // ==================== TASK CONVERSIONS ====================
    
    /**
     * Convert TaskEntity thành Task model
     */
    public static Task toTask(TaskEntity entity) {
        if (entity == null) return null;
        
        Task task = new Task();
        task.setId(entity.getId());
        task.setTitle(entity.getTitle());
        task.setDescription(entity.getDescription());
        task.setStartTime(entity.getStartTimeAsDate());
        task.setEndTime(entity.getEndTimeAsDate());
        task.setCompleted(entity.isCompleted());
        task.setPriority(entity.getPriority());
        task.setCategoryId(entity.getCategoryId());
        task.setUserId(entity.getUserId());
        task.setCreatedAt(entity.getCreatedAtAsDate());
        task.setUpdatedAt(entity.getUpdatedAtAsDate());
        
        return task;
    }
    
    /**
     * Convert Task model thành TaskEntity
     */
    public static TaskEntity toTaskEntity(Task task) {
        if (task == null) return null;
        
        TaskEntity entity = new TaskEntity();
        entity.setId(task.getId());
        entity.setTitle(task.getTitle());
        entity.setDescription(task.getDescription());
        entity.setStartTimeFromDate(task.getStartTime());
        entity.setEndTimeFromDate(task.getEndTime());
        entity.setCompleted(task.isCompleted());
        entity.setPriority(task.getPriority());
        entity.setCategoryId(task.getCategoryId());
        entity.setUserId(task.getUserId());
        entity.setCreatedAt(task.getCreatedAt() != null ? task.getCreatedAt().getTime() : null);
        entity.setUpdatedAt(task.getUpdatedAt() != null ? task.getUpdatedAt().getTime() : null);
        
        return entity;
    }
    
    /**
     * Convert List<TaskEntity> thành List<Task>
     */
    public static List<Task> toTaskList(List<TaskEntity> entities) {
        if (entities == null) return null;
        
        List<Task> tasks = new ArrayList<>();
        for (TaskEntity entity : entities) {
            tasks.add(toTask(entity));
        }
        return tasks;
    }
}
