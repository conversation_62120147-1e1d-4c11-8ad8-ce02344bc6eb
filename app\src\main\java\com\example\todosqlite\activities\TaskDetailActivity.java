package com.example.todosqlite.activities;

import android.app.AlertDialog;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.todosqlite.R;
import com.example.todosqlite.adapters.SubTaskAdapter;
import com.example.todosqlite.database.TaskDAO;
import com.example.todosqlite.database.SubTaskDAO;
import com.example.todosqlite.models.SubTask;
import com.example.todosqlite.models.Task;
import com.example.todosqlite.utils.DateUtils;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.List;

/**
 * Activity hiển thị chi tiết task và quản lý subtasks
 */
public class TaskDetailActivity extends AppCompatActivity {
    
    private TextView tvTaskTitle, tvTaskDescription, tvTaskTime, tvTaskPriority;
    private RecyclerView rvSubTasks;
    private FloatingActionButton fabAddSubTask;
    
    private TaskDAO taskDAO;
    private SubTaskDAO subTaskDAO;
    private SubTaskAdapter subTaskAdapter;
    private List<SubTask> subTaskList;
    
    private Task currentTask;
    private int taskId;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_task_detail);
        
        // Get task ID from intent
        taskId = getIntent().getIntExtra("task_id", -1);
        if (taskId == -1) {
            Toast.makeText(this, "Lỗi: Không tìm thấy nhiệm vụ", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        initViews();
        setupToolbar();
        setupRecyclerView();
        setupClickListeners();
        
        taskDAO = new TaskDAO(this);
        subTaskDAO = new SubTaskDAO(this);
        subTaskList = new ArrayList<>();
        
        loadTaskDetails();
        loadSubTasks();
    }
    
    private void initViews() {
        tvTaskTitle = findViewById(R.id.tv_task_title);
        tvTaskDescription = findViewById(R.id.tv_task_description);
        tvTaskTime = findViewById(R.id.tv_task_time);
        tvTaskPriority = findViewById(R.id.tv_task_priority);
        rvSubTasks = findViewById(R.id.rv_subtasks);
        fabAddSubTask = findViewById(R.id.fab_add_subtask);
    }
    
    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Chi tiết nhiệm vụ");
        }
    }
    
    private void setupRecyclerView() {
        subTaskAdapter = new SubTaskAdapter(this, subTaskList);
        rvSubTasks.setLayoutManager(new LinearLayoutManager(this));
        rvSubTasks.setAdapter(subTaskAdapter);
        
        subTaskAdapter.setOnSubTaskClickListener(new SubTaskAdapter.OnSubTaskClickListener() {
            @Override
            public void onSubTaskCompleteToggle(SubTask subTask) {
                toggleSubTaskCompletion(subTask);
            }
            
            @Override
            public void onSubTaskDelete(SubTask subTask) {
                deleteSubTask(subTask);
            }
        });
    }
    
    private void setupClickListeners() {
        fabAddSubTask.setOnClickListener(v -> showAddSubTaskDialog());
    }
    
    private void loadTaskDetails() {
        new Thread(() -> {
            try {
                Task task = taskDAO.getTaskById(taskId);
                if (task != null) {
                    currentTask = task;
                    runOnUiThread(() -> displayTaskDetails(task));
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "Không tìm thấy nhiệm vụ", Toast.LENGTH_SHORT).show();
                        finish();
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "Lỗi khi tải thông tin nhiệm vụ", Toast.LENGTH_SHORT).show();
                    finish();
                });
            }
        }).start();
    }
    
    private void displayTaskDetails(Task task) {
        tvTaskTitle.setText(task.getTitle());
        
        if (task.getDescription() != null && !task.getDescription().isEmpty()) {
            tvTaskDescription.setText(task.getDescription());
            tvTaskDescription.setVisibility(View.VISIBLE);
        } else {
            tvTaskDescription.setVisibility(View.GONE);
        }
        
        if (task.getStartTime() != null) {
            tvTaskTime.setText(DateUtils.getRelativeTimeText(task.getStartTime()));
            tvTaskTime.setVisibility(View.VISIBLE);
        } else {
            tvTaskTime.setVisibility(View.GONE);
        }
        
        // Set priority
        String priorityText;
        switch (task.getPriority()) {
            case 1:
                priorityText = "Độ ưu tiên: Thấp";
                break;
            case 2:
                priorityText = "Độ ưu tiên: Trung bình";
                break;
            case 3:
            default:
                priorityText = "Độ ưu tiên: Cao";
                break;
        }
        tvTaskPriority.setText(priorityText);
    }
    
    private void loadSubTasks() {
        new Thread(() -> {
            try {
                List<SubTask> subTasks = subTaskDAO.getSubTasksByParentTask(taskId);
                runOnUiThread(() -> {
                    subTaskList.clear();
                    subTaskList.addAll(subTasks);
                    subTaskAdapter.notifyDataSetChanged();
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }
    
    private void showAddSubTaskDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Thêm nhiệm vụ con");
        
        final EditText input = new EditText(this);
        input.setHint("Nhập tên nhiệm vụ con (VD: Vo gạo, Cắm điện)");
        builder.setView(input);
        
        builder.setPositiveButton("Thêm", (dialog, which) -> {
            String title = input.getText().toString().trim();
            if (!title.isEmpty()) {
                addSubTask(title);
            } else {
                Toast.makeText(this, "Vui lòng nhập tên nhiệm vụ con", Toast.LENGTH_SHORT).show();
            }
        });
        
        builder.setNegativeButton("Hủy", null);
        builder.show();
    }
    
    private void addSubTask(String title) {
        new Thread(() -> {
            try {
                SubTask subTask = new SubTask(title, taskId);
                long subTaskId = subTaskDAO.insertSubTask(subTask);
                
                if (subTaskId > 0) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "Đã thêm nhiệm vụ con", Toast.LENGTH_SHORT).show();
                        loadSubTasks(); // Reload list
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "Lỗi khi thêm nhiệm vụ con", Toast.LENGTH_SHORT).show();
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "Lỗi khi thêm nhiệm vụ con", Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }
    
    private void toggleSubTaskCompletion(SubTask subTask) {
        new Thread(() -> {
            try {
                subTask.setCompleted(!subTask.isCompleted());
                subTaskDAO.updateSubTask(subTask);
                
                runOnUiThread(() -> {
                    subTaskAdapter.notifyDataSetChanged();
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }
    
    private void deleteSubTask(SubTask subTask) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Xác nhận xóa")
                .setMessage("Bạn có chắc chắn muốn xóa nhiệm vụ con \"" + subTask.getTitle() + "\"?")
                .setPositiveButton("Xóa", (dialog, which) -> {
                    new Thread(() -> {
                        try {
                            subTaskDAO.deleteSubTask(subTask.getId());
                            
                            runOnUiThread(() -> {
                                subTaskList.remove(subTask);
                                subTaskAdapter.notifyDataSetChanged();
                                Toast.makeText(this, "Đã xóa nhiệm vụ con", Toast.LENGTH_SHORT).show();
                            });
                        } catch (Exception e) {
                            e.printStackTrace();
                            runOnUiThread(() -> {
                                Toast.makeText(this, "Lỗi khi xóa nhiệm vụ con", Toast.LENGTH_SHORT).show();
                            });
                        }
                    }).start();
                })
                .setNegativeButton("Hủy", null)
                .show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
