#Tue Jul 01 16:22:31 ICT 2025
path.4=15/classes.dex
path.3=11/classes.dex
path.2=6/classes.dex
renamed.9=classes10.dex
path.1=0/classes.dex
renamed.8=classes9.dex
path.8=5/classes.dex
path.7=9/classes.dex
path.6=4/classes.dex
path.5=3/classes.dex
path.0=classes.dex
base.4=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
base.3=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\11\\classes.dex
base.2=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
base.1=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.0=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
base.9=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
path.9=classes2.dex
renamed.7=classes8.dex
base.8=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
renamed.6=classes7.dex
base.7=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\9\\classes.dex
renamed.5=classes6.dex
base.6=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\4\\classes.dex
renamed.4=classes5.dex
base.5=D\:\\TodoSqlite\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
