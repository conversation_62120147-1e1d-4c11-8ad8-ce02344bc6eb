package com.example.todosqlite.models;

import java.util.Date;

/**
 * Model class cho Task (Nhiệm vụ)
 */
public class Task {
    private int id;
    private String title;           // Tiêu đề
    private String description;     // Mô tả
    private Date startTime;         // Thời gian bắt đầu
    private Date endTime;           // Thời gian kết thúc
    private boolean isCompleted;    // Tình trạng hoàn thành
    private int priority;           // Độ ưu tiên (1-5)
    private int categoryId;         // ID danh mục
    private int userId;             // ID người dùng
    private Date createdAt;         // Thời gian tạo
    private Date updatedAt;         // Thời gian cập nhật

    // Constructor mặc định
    public Task() {
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.isCompleted = false;
        this.priority = 1;
    }

    // Constructor đầy đủ
    public Task(String title, String description, Date startTime, Date endTime, 
                int categoryId, int userId) {
        this();
        this.title = title;
        this.description = description;
        this.startTime = startTime;
        this.endTime = endTime;
        this.categoryId = categoryId;
        this.userId = userId;
    }

    // Getters và Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        this.updatedAt = new Date();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = new Date();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
        this.updatedAt = new Date();
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
        this.updatedAt = new Date();
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        isCompleted = completed;
        this.updatedAt = new Date();
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
        this.updatedAt = new Date();
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
        this.updatedAt = new Date();
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
        this.updatedAt = new Date();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "Task{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", isCompleted=" + isCompleted +
                ", priority=" + priority +
                '}';
    }
}
